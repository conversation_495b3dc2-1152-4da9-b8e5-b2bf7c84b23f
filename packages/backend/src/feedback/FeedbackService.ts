import { sql } from "pigbot-core/src/database";
import { cache } from "pigbot-core/src/Cache";
import { UserContext } from "pigbot-core/src/UserContext";
import { EFFICIENCY_REPORT_TYPE, GOOD_RESPONSE } from "pigbot-core/src/Constants";
import { textCompletionChatGpt } from "pigbot-core/src/llm/ChatGptClient";
import { WebClient } from "@slack/web-api";
import Config from "pigbot-core/src/Config";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import { getSection } from "pigbot-core/src/feedback/splitMarkdown";
import { convertGFMToSlack } from "../utils/gfmToSlack";
// This is necessary for mocking internal calls: https://stackoverflow.com/a/55193363
import * as self from "./FeedbackService";
import path from "node:path";
import { EffReportSummaryReqResData } from "pigbot-core/src/eff-report/EfficiencyReportService";
import { P, match } from "ts-pattern";
import { EfficiencyReportData } from "pigbot-core/src/EfficiencyReportData";
import { extractTextFromStream } from "pigbot-core/src/utils/extractTextFromStream";
import logger from "pigbot-core/src/logger";

export type AllFeedback = {
	[key: string]: string;
};

export interface SaveFeedback {
	responseId: string;
	sectionId: string;
	feedback: string;
}

type ResponseDataV1 = {
	responseText: string;
	reportData: EfficiencyReportData;
};

/**
 * Types of response data that can be persisted in the feedback table
 */
export type PersistedResponseData = EffReportSummaryReqResData | ResponseDataV1;

export async function sendFeedbackToSlack(sectionId: string, responseText: string, feedback: string, userContext: UserContext) {
	try {
		const slackClient = new WebClient(Config.SLACK_BOT_TOKEN!);

		const section = getSection(sectionId, responseText);

		// Quote the section
		const sectionQuoted = convertGFMToSlack(section)
			.split("\n")
			.map((line) => `> ${line}`)
			.join("\n");

		await slackClient.chat.postMessage({
			channel: "#vfa-feedback",
			text: renderTemplateFile(path.resolve(__dirname, "slack-feedback.hbs"), {
				userContext,
				feedback: feedback.replace(GOOD_RESPONSE, ":thumbsup:"), // Replace GOOD_RESPONSE with a thumbs up emoji
				sectionQuoted,
			}),
		});
	} catch (error) {
		logger.error("Error sending message to Slack: ", error);
	}
}

export async function saveFeedback({ responseId, sectionId, feedback }: SaveFeedback, userContext: UserContext) {
	// We still allow to save feedback in both v1 and v2 vfa
	const response = await cache.get<EffReportSummaryReqResData>(responseId);
	if (response === undefined) {
		throw new Error(`Response data not found for key ${responseId}`);
	}

	// Get data based on version of vfa
	const { reqResData, responseText } = match(response)
		.with({ version: P._ }, (value) => ({
			reqResData: value,
			responseText: extractTextFromStream(value.internalResponseStreamArray),
		}))
		.exhaustive();

	// Send feedback to Slack only if the SLACK_BOT_TOKEN is defined. Normally in dev mode it should be empty.
	if (Config.SLACK_BOT_TOKEN !== undefined) {
		// Using self is necessary for mocking internal calls: https://stackoverflow.com/a/55193363
		await self.sendFeedbackToSlack(sectionId, responseText, feedback, userContext);
	}

	// There is a type error caused by unknown in `context: Record<string, unknown>`
	// This bypasses it
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const reqResDataAny = reqResData as any;

	const [{ feedback: allFeedback }] = await sql<
		[
			{
				feedback: AllFeedback;
			},
		]
	>`INSERT INTO response_feedback (id, farm_id, root_id, response, feedback, type, summary, farm_name, author_id, author_name)
		VALUES (${responseId},
						${userContext.farmId},
						${userContext.rootId},
						${sql.json(reqResDataAny)},
						${sql.json({ [sectionId]: feedback })},
						${EFFICIENCY_REPORT_TYPE},
						${feedback.slice(0, 50)},
						${userContext.farmName},
						${userContext.userId},
						${userContext.name})
		ON CONFLICT (id) DO UPDATE SET feedback = response_feedback.feedback || ${sql.json({ [sectionId]: feedback })}
		RETURNING feedback`;

	// Using self is necessary for mocking internal calls: https://stackoverflow.com/a/55193363
	self.summarizeFeedback(responseId, allFeedback, userContext).catch(logger.error);
}

export interface DeleteFeedback {
	responseId: string;
	sectionId: string;
}

export async function deleteFeedback({ responseId, sectionId }: DeleteFeedback, userContext: UserContext) {
	await sql.begin(async (sql) => {
		const [{ feedback }] = await sql<{ feedback: AllFeedback }[]>`UPDATE response_feedback
																																	SET feedback = feedback - ${sectionId}
																																	WHERE id = ${responseId}
																																	RETURNING feedback`;

		const noFeedback = Object.keys(feedback).length === 0;
		if (noFeedback) {
			await sql`DELETE
								FROM response_feedback
								WHERE feedback = '{}'
									AND id = ${responseId}`;
		} else {
			// Using self is necessary for mocking internal calls: https://stackoverflow.com/a/55193363
			self.summarizeFeedback(responseId, feedback, userContext).catch(logger.error);
		}
	});
}

export async function summarizeFeedback(responseId: string, allFeedback: AllFeedback, userContext: UserContext) {
	const { response: summarizedFeedback } = await textCompletionChatGpt({
		messages: [
			{
				role: "user",
				content: renderTemplateFile(path.resolve(__dirname, "summarize-feedback.hbs"), { feedback: Object.values(allFeedback) }),
			},
		],
		metricData: {
			activity: "summarize-feedback",
			action: "summarize-feedback",
			...userContext,
		},
		cacheIdx: 0,
		maxToken: 100,
	});

	await sql`UPDATE response_feedback
						SET summary = ${summarizedFeedback}
						WHERE id = ${responseId};`;
}
