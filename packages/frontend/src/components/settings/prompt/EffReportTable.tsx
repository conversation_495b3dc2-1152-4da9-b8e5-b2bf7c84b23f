import { observer } from 'mobx-react-lite';
import { EfficiencyReportData } from 'pigbot-core/src/EfficiencyReportData';
import Table from 'antd/lib/table';
import 'antd/lib/table/style/index.less';
import React from 'react';
import { getPeriodNamings } from 'pigbot-core/src/period-names/PeriodNamings';

const EffReportTable: React.FC<{
	effReportResponse: EfficiencyReportData;
}> = observer((props) => {
	const periodNamings = getPeriodNamings(props.effReportResponse.periods.slice(0, -1), props.effReportResponse.language, 'TODO'); // TODO dateFormat
	const columns: object[] = [];
	for (let i = 0; i < props.effReportResponse.periods.length - 1; i++) {
		//we ignore last period, as it is ignored by llm.
		columns.push({
			title: periodNamings[i].label,
			key: 'index' + i.toString(),
			dataIndex: 'index' + i.toString(),
			width: 50,
		});
	}
	columns.unshift({
		title: 'Goal',
		key: 'goal',
		dataIndex: 'goal',
		width: 50,
	});
	columns.unshift({
		title: 'Name',
		key: 'name',
		dataIndex: 'name',
		width: 150,
	});

	const dataSource = props.effReportResponse.sections.reduce(
		(acc, item) => {
			const thingsToAdd = [];
			thingsToAdd.push({
				name: item.label,
			});
			const items = item.kpis.map((kpi) => {
				const kpiO: { [key: string]: string } = kpi.periodValues.reduce(
					(acc, item, index) => {
						acc['index' + index] = item.value?.toString() ?? '';
						return acc;
					},
					{} as { [key: string]: string },
				);
				kpiO['name'] = kpi.label;
				const goal = kpi.periodValues[0]?.goalOpt;
				kpiO['goal'] = goal !== null ? goal?.toString() : '';

				return kpiO;
			});

			return acc.concat(items);
		},
		[] as Array<{ [key: string]: string }>,
	);

	return <Table dataSource={dataSource} columns={columns} size='small' scroll={{ y: 640 }} pagination={false}></Table>;
});

export default EffReportTable;
