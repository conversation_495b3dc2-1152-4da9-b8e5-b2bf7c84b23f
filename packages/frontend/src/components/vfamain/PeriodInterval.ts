import { getDatePeriods, PeriodType } from 'pigbot-core/src/vfamain/Periods';
import moment from 'moment';

export type PeriodInterval = {
	from: moment.Moment;
	to: moment.Moment;
};

export function getPeriodInterval(periodType: PeriodType, to: moment.Moment, periodLength: number): PeriodInterval {
	const periods = getDatePeriods({ periodType, periodSince: to.toDate() }, periodLength);

	return {
		from: moment(periods[periods.length - 1].from),
		to: moment(periods[0].to),
	};
}
