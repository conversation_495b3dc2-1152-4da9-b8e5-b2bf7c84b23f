import { proxyActivities } from "@temporalio/workflow";
import { FarmId } from "../FarmId";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities } from "./CfLinkActivities";

export async function GetDateFormat(farmId: FarmId, langCode: string, userId: number): Promise<string> {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "10m",
		taskQueue: getCfLinkQueue(farmId.env),
	});

	const dateFormat = await activities.GetDateFormat(farmId.farmId, langCode, userId);

	return dateFormat;
}
