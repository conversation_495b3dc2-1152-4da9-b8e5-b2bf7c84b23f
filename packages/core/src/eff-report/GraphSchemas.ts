import { z } from "zod";

/**
 * Part of the schema that is the same for all chart types.
 */
const commonSchema = z.object({
	title: z.string().describe("The main title displayed above the chart"),
	explanation: z.string().describe("Explanation displayed below the chart."),
});

/**
 * Pie chart schema
 */
export const pieChartSchema = commonSchema.merge(
	z.object({
		series: z
			.array(
				z.object({
					name: z.string().describe("Name of the slice (e.g., category, reason)"),
					value: z.number().describe("Numerical value or percentage of the slice"),
				}),
			)
			.min(2, "Must have at least 2 slices")
			.describe("An array of objects, each representing a slice of the pie"),
		seriesUnits: z.string().describe("Name of the units in this graph"),
	}),
);

/**
 *  Common schema for all chart types with series
 */
const seriesSchema = commonSchema.merge(
	z.object({
		yAxisName: z.string().describe("The label describing the Y-axis"),
		yAxisUnit: z.string().describe("The unit for the Y-axis values. Appended to axis labels and tooltips."),
		periodNames: z
			.array(z.string())
			.describe(
				"Labels for the X-axis (categories or time periods). Series data is aligned to these categories/periods. Minimum 4 categories/periods. Maximum 6 categories/periods. Always start with oldest and end with newest period. Write these in the target language.",
			),
	}),
);

/**
 * Stacked line graph schema
 */
export const stackedLineGraphSchema = seriesSchema.merge(
	z.object({
		series: z
			.array(
				z.object({
					title: z.string().describe("Name of the data series"),
					data: z.array(z.number()).describe("Numerical data points for periods defined in the field `periodNames`."),
					goal: z.number().optional().describe("Optional: A target value for this series (dashed line)"),
				}),
			)
			.min(4, "Must have at least 4 periods")
			.describe("An array containing one object for each line on the graph"),
	}),
);

/**
 * Stacked bar chart schema
 */
export const stackedBarChartSchema = seriesSchema.merge(
	z.object({
		series: z
			.array(
				z.object({
					title: z.string().describe("Name of the segment (e.g., 'Feed Type A', 'Reason X')"),
					data: z.array(z.number()).describe("Numerical data points corresponding to each period/category name in periodNames"),
				}),
			)
			.min(4, "Must have at least 4 periods")
			.describe("An array containing one object for each segment *within* a bar"),
	}),
);

// Derive types from schemas
export type StackedLineGraphParams = z.infer<typeof stackedLineGraphSchema>;
export type PieChartParams = z.infer<typeof pieChartSchema>;
export type StackedBarChartParams = z.infer<typeof stackedBarChartSchema>;
