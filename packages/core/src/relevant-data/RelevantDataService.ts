import { FarmHoldingId } from "../FarmId";
import { PIGBOT_WORKER_QUEUE, startOrAwaitWorkflow } from "../temporal/temporal";
import { cache, hash } from "../Cache";
import { ExtraDataPoints, KpiResultData, MedicineUsageData, Period, PregnancyFailureReason } from "../cf-link/CfLinkActivities";
import { renderTemplateFile } from "../utils/renderTemplateFile";
import Handlebars from "handlebars";
import { match } from "ts-pattern";
import logger from "../logger";
import { kpiToText } from "../data-to-text/DataToText";
import path from "node:path";
import { ensureDir, readdir, readFile } from "fs-extra";
import { coreResourcesDir } from "../../resources/CoreResources";
import { GetCfLinkData } from "../cf-link/GetCfLinkData";
import { PeriodsContext } from "../period-names/PeriodsContext";

type RelevantData = {
	additionalKpis: KpiResultData[];
	medicineUsages?: MedicineUsageData[];
	pregnancyFailures?: PregnancyFailureReason[];
};

export type GetRelevantDataResponse = {
	relevantData: RelevantData;
	kpisAnalysis: KpiStaticAnalysis[];
	causesAnalysis: { id: number; cause: string; causeConfirmationDPs: string[]; actionSelectionDPs: string[] }[];
};

export type CauseStaticAnalysis = {
	cause: string;
	id: number;
	causeAnalysis?: {
		selectedDataPoints: string[];
		explanation: string;
	};
	actionAnalysis?: {
		selectedDataPoints: string[];
		explanation: string;
	};
	dataPointRelevancy: Record<string, number>;
};

export type KpiStaticAnalysis = { code: string; name: string; causes: { cause: string; id: number }[] };

/**
 * Loads causes from cause directory
 */
export async function loadCauses() {
	const allCauses: CauseStaticAnalysis[] = [];

	const causeDir = path.resolve(coreResourcesDir, "cause");
	await ensureDir(causeDir); // Ensure cause directory exists

	// Load existing causes from cause directory
	try {
		const causeFiles = await readdir(causeDir);
		for (const file of causeFiles) {
			if (file.endsWith(".json")) {
				const filePath = path.join(causeDir, file);
				const fileContent = await readFile(filePath, "utf-8");
				const cause = JSON.parse(fileContent) as CauseStaticAnalysis;
				if (cause.dataPointRelevancy === undefined) {
					cause.dataPointRelevancy = {};
				}
				allCauses.push(cause);
			}
		}
	} catch (error) {
		logger.error("Error loading existing causes:", error);
	}
	return allCauses.sort((a, b) => a.id - b.id);
}

/**
 * Collects causes that have been analyzed, strips unnecessary data and combines cause and action analysis
 */
async function processAnalyzedCauses() {
	const allCauses = await loadCauses();

	return Object.fromEntries(
		allCauses.flatMap((cause) => {
			return cause.causeAnalysis !== undefined && cause.actionAnalysis !== undefined
				? [
						[
							cause.id,
							{
								id: cause.id,
								cause: cause.cause,
								causeConfirmationDPs: cause.causeAnalysis.selectedDataPoints,
								actionSelectionDPs: cause.actionAnalysis.selectedDataPoints,
								combinedDPs: Array.from(new Set([...cause.causeAnalysis.selectedDataPoints, ...cause.actionAnalysis.selectedDataPoints])),
							},
						],
					]
				: [];
		}),
	);
}

// Processing only needs to be done once and then is stored in memory
const analyzedCausesPromise = processAnalyzedCauses();

/**
 * Collects kpis that have been analyzed and creates a map of kpi code to kpi
 */
async function processKpis() {
	const allKpis: KpiStaticAnalysis[] = [];

	const kpiDir = path.resolve(coreResourcesDir, "kpi");
	await ensureDir(kpiDir); // Ensure kpi directory exists

	// Load existing causes from cause directory
	try {
		const kpiFiles = await readdir(kpiDir);
		for (const file of kpiFiles) {
			if (file.endsWith(".json")) {
				const filePath = path.join(kpiDir, file);
				const fileContent = await readFile(filePath, "utf-8");
				const cause = JSON.parse(fileContent) as KpiStaticAnalysis;
				allKpis.push(cause);
			}
		}
	} catch (error) {
		throw Object.assign(new Error("Error loading existing kpis"), { cause: error });
	}

	return Object.fromEntries(allKpis.map((kpi) => [kpi.code, kpi]));
}

// Processing only needs to be done once and then is stored in memory
const analyzedKpisPromise = processKpis();

/**
 * Calls cf-pigbot-link to ge relevant data based on problematic kpis
 * @param params
 */
export async function getRelevantData(params: {
	farmHoldingId: FarmHoldingId;
	langCode: string;
	periods: Period[];
	indicatingKPIs: string[];
	reportKpis: string[];
	breeds: string | null;
}): Promise<GetRelevantDataResponse> {
	const analyzedCauses = await analyzedCausesPromise;
	const analyzedKpis = await analyzedKpisPromise;

	// Collect analysis for problematic kpis
	const kpisAnalysis = params.indicatingKPIs.flatMap((kpi) => {
		const analysis = analyzedKpis[kpi];
		if (analysis === undefined) {
			logger.warn(`KPI ${kpi} not found in analyzedKpis`);
			return [];
		} else {
			return [analysis];
		}
	});

	// Collect ids from all problematic kpis to get a list of all possible causes
	const causesIds = Array.from(new Set(kpisAnalysis.flatMap((kpiAndCauses) => kpiAndCauses.causes.map((c) => c.id))));

	// Collect analysis for possible causes
	const causesAnalysis = causesIds
		.sort((a, b) => a - b)
		.flatMap((id) => {
			const cause = analyzedCauses[id];
			if (cause === undefined) {
				logger.error(`Cause ${id} not found in analyzedCauses`);
				return [];
			} else {
				return [cause];
			}
		});

	const reportKpisSet = new Set(params.reportKpis);

	// Get data points from possible causes that are not already in the report kpis
	const dataPointsToDeterminedByCauses = Array.from(new Set(causesAnalysis.flatMap((cause) => cause.combinedDPs))).filter(
		(dp) => !reportKpisSet.has(dp),
	);

	// TODO Consider if this is still needed if we improve KPI associations generation
	// These are data points that are often relevant but not picked up by the AI.
	const dataPointsToAlwaysGet = [
		ExtraDataPoints.MEDICINE_USAGE_SUMMARY, // Probably often relevant but AI rarely picks it up.
		"SOW_PARITY_AVG_NUMBER", // Sophie mentions it, but AI almost never picks it up
		"SOWS_NPD", //Following kpis were mentioned in Sophies feedback, but did not have description in CF. Description was added to the link, but the relevance software must be run. TODO: Remove this when the relevance is recreated
		"NPD_LATE_ABORTION_PER_LITTER",
		"NPD_PER_LITTER",
		"NPD_SERVING2DEAD_PER_LITTER",
		"NPD_SERVING2EXIT_PER_LITTER",
		"NPD_SERVING2SERVING_PER_LITTER",
		"NPD_SERVING2SOLD_PER_LITTER",
		"NPD_WEANING2DEAD_PER_LITTER",
		"NPD_WEANING2SERVING_PER_LITTER",
		"NPD_WEANING2SOLD_PER_LITTER",
		"SOW_NPD_BASED_LITTER_SOW_YEAR",
	];

	const workflowParams = {
		...params,
		dataPointsToGet: [...dataPointsToDeterminedByCauses, ...dataPointsToAlwaysGet].sort(), // Sorted to help with caching
	};

	const requestKey = `getCfLinkData-${hash(params)}`;
	const cachedResponse = await cache.get<GetRelevantDataResponse>(requestKey);

	if (cachedResponse) {
		return cachedResponse;
	}

	const relevantData = await startOrAwaitWorkflow(PIGBOT_WORKER_QUEUE, requestKey, GetCfLinkData, [workflowParams], "15m");

	const response = {
		relevantData,

		// kpisAnalysis and causesAnalysis are returned for debugging purposes
		kpisAnalysis,
		causesAnalysis: causesAnalysis.map((c) => ({ ...c, combinedDPs: undefined })),
	};

	await cache.set(requestKey, response, 7 * 24 * 60 * 60 * 1000); // Cache for one week

	return response;
}

/**
 * Converts relevant data to text
 */
export function relevantDataToText(data: RelevantData, periodsContext: PeriodsContext) {
	Handlebars.registerHelper("translateAnimalType", (animalType) => {
		return match(animalType)
			.with("SOW", () => "sows")
			.with("GILT", () => "young breeding animals")
			.with("PIGL", () => "sows")
			.with("BOAR", () => "boars")
			.with("GROUP", () => "group animals")
			.otherwise(() => {
				logger.error(`Unknown animal type in convertMedicineUsagesToText: ${animalType}`);
				return "unknown animals";
			});
	});

	const context = {
		...data,
		...periodsContext,
	};

	return {
		additionalKpis:
			data.additionalKpis.length > 0
				? kpiToText(
						data.additionalKpis.map((kpi) => ({
							...kpi,
							label: kpi.name,
							isMoreBetterOpt: null,
							periodValues: kpi.values.map((value) => ({ value, goalOpt: null })),
						})),
						periodsContext.periodCodes,
					)
				: null,
		medicineUsages:
			data.medicineUsages && data.medicineUsages.length > 0
				? renderTemplateFile(path.resolve(__dirname, "medicineUsagesToText.hbs"), context)
				: null,
		pregnancyFailures: data.pregnancyFailures ? renderTemplateFile(path.resolve(__dirname, "pregnancyFailuresToText.hbs"), context) : null,
	};
}
