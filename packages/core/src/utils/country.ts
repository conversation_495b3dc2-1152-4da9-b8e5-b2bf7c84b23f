import logger from "../logger";

// Use Intl.DisplayNames to get the country name in English
const countryNames = new Intl.DisplayNames(["en"], { type: "region" });

/**
 * Gets the English country name from a country code using Node.js Intl API
 * @param countryCode ISO 3166-1 alpha-2 country code (e.g., 'US', 'DE')
 * @returns The English name of the country
 */
export function getCountryNameFromCode(countryCode: string): string {
	try {
		return countryNames.of(countryCode.toUpperCase())!;
	} catch (error) {
		logger.warn(`Failed to get country name for code: ${countryCode}`, error);
		return countryCode; // Fallback to the code if there's an error
	}
}
