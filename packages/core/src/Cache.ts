import * as cacheManager from "cache-manager";
import { createHash } from "crypto";
import { ioRedisStore, RedisCache } from "@tirke/node-cache-manager-ioredis";
import { localRedisOptions, sharedRedisOptions } from "./RedisClient";
import logger from "./logger";
import * as process from "node:process";

/**
 * This allows disabling caching of responses in development
 */
export const ResponseCacheEnabled = process.env["CACHE_RESPONSES"] !== "false";

if (!ResponseCacheEnabled) {
	logger.warn("Response caching is disabled");
}

/**
 * Cache using local redis.
 */
export const cachePromise = cacheManager.caching(ioRedisStore, {
	instanceConfig: localRedisOptions,
});

/**
 * Cache using shared redis.
 */
export const sharedCachePromise = cacheManager.caching(ioRedisStore, {
	instanceConfig: sharedRedisOptions,
});

class CacheInstance {
	constructor(private cachePromise: Promise<RedisCache>) {}

	async get<T>(key: string) {
		if (!ResponseCacheEnabled) {
			return undefined;
		}
		return await (await this.cachePromise).get<T>(key);
	}

	async set(key: string, value: unknown, ttl?: number) {
		if (!ResponseCacheEnabled) {
			return;
		}
		await (await this.cachePromise).set(key, value, ttl);
	}
}

/**
 * Cache for data that are always the same given the same input parameters. E.g.: LLM calls.

 * In tests, these calls are cached in a shared redis instance to avoid calling the LLMs multiple times.
 */
export const deterministicCache = new CacheInstance(process.env.NODE_ENV === "test" ? sharedCachePromise : cachePromise);

/**
 * Normal cache for non-deterministic data.
 */
export const cache = new CacheInstance(cachePromise);

export function hash(input: unknown) {
	return createHash("sha256").update(JSON.stringify(input)).digest("hex");
}
