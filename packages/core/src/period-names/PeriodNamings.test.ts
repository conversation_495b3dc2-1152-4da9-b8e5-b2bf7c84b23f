import { describe, expect, test } from "@jest/globals";
import { getPeriodNamings } from "./PeriodNamings";

describe("getPeriodNamings", () => {
	describe("Calendar month periods (Type A)", () => {
		test("should handle single calendar month periods in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
				{ from: "2024-03-01", to: "2024-03-31" },
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
				{
					code: "2024-03-01–2024-03-31",
					from: "2024-03-01",
					to: "2024-03-31",
					label: "Mar",
					labelDesc: undefined,
					textName: "March",
				},
			]);
		});

		test("should handle calendar month periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan.",
					labelDesc: undefined,
					textName: "Januar",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb.",
					labelDesc: undefined,
					textName: "Februar",
				},
			]);
		});

		test("should handle calendar month periods in French", () => {
			const periods = [
				{ from: "2024-06-01", to: "2024-06-30" },
				{ from: "2024-07-01", to: "2024-07-31" },
			];

			const result = getPeriodNamings(periods, "fr", "standard");

			expect(result).toEqual([
				{
					code: "2024-06-01–2024-06-30",
					from: "2024-06-01",
					to: "2024-06-30",
					label: "juin",
					labelDesc: undefined,
					textName: "juin",
				},
				{
					code: "2024-07-01–2024-07-31",
					from: "2024-07-01",
					to: "2024-07-31",
					label: "juil.",
					labelDesc: undefined,
					textName: "juillet",
				},
			]);
		});
	});

	describe("Range of calendar months periods (Type B)", () => {
		test("should handle range of calendar months in English", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-03-31" }, // Jan-Mar
				{ from: "2024-04-01", to: "2024-06-30" }, // Apr-Jun
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-03-31",
					from: "2024-01-01",
					to: "2024-03-31",
					label: "Jan–Mar",
					labelDesc: undefined,
					textName: "January–March",
				},
				{
					code: "2024-04-01–2024-06-30",
					from: "2024-04-01",
					to: "2024-06-30",
					label: "Apr–Jun",
					labelDesc: undefined,
					textName: "April–June",
				},
			]);
		});

		test("should handle range of calendar months in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-02-29" }, // Jan-Feb
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-02-29",
					from: "2024-01-01",
					to: "2024-02-29",
					label: "Jan.–Feb.",
					labelDesc: undefined,
					textName: "Januar–Februar",
				},
			]);
		});

		test("should handle cross-year range of calendar months", () => {
			const periods = [
				{ from: "2023-11-01", to: "2024-01-31" }, // Nov-Jan
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-11-01–2024-01-31",
					from: "2023-11-01",
					to: "2024-01-31",
					label: "Nov–Jan",
					labelDesc: undefined,
					textName: "November–January",
				},
			]);
		});
	});

	describe("Same length periods (Type C)", () => {
		test("should handle weekly periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
				{ from: "2024-01-15", to: "2024-01-21" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 1–Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 8–Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14",
				},
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 15–Jan 21",
					labelDesc: "1-week periods",
					textName: "latest 1-week period",
				},
			]);
		});

		test("should handle 4-week periods", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-28" }, // 28 days = 4 weeks
				{ from: "2024-01-29", to: "2024-02-25" }, // 28 days = 4 weeks
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-28",
					from: "2024-01-01",
					to: "2024-01-28",
					label: "Jan 1–Jan 28",
					labelDesc: "4-week periods",
					textName: "4-week period ending January 28",
				},
				{
					code: "2024-01-29–2024-02-25",
					from: "2024-01-29",
					to: "2024-02-25",
					label: "Jan 29–Feb 25",
					labelDesc: "4-week periods",
					textName: "latest 4-week period",
				},
			]);
		});

		test("should handle non-weekly periods (days)", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-20" }, // 20 days
				{ from: "2024-01-21", to: "2024-02-09" }, // 20 days
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-20",
					from: "2024-01-01",
					to: "2024-01-20",
					label: "Jan 1–Jan 20",
					labelDesc: "20-day periods",
					textName: "20-day period ending January 20",
				},
				{
					code: "2024-01-21–2024-02-09",
					from: "2024-01-21",
					to: "2024-02-09",
					label: "Jan 21–Feb 9",
					labelDesc: "20-day periods",
					textName: "latest 20-day period",
				},
			]);
		});

		test("should handle same length periods in German", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "de", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "1. Jan.–7. Jan.",
					labelDesc: "1-week periods",
					textName: "1-week period ending 7. Januar",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "8. Jan.–14. Jan.",
					labelDesc: "1-week periods",
					textName: "latest 1-week period",
				},
			]);
		});
	});

	describe("Edge cases", () => {
		test("should handle empty periods array", () => {
			const result = getPeriodNamings([], "en", "standard");
			expect(result).toEqual([]);
		});

		test("should handle single period", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
			]);
		});

		test("should handle invalid language code gracefully", () => {
			const periods = [{ from: "2024-01-01", to: "2024-01-31" }];

			// Should not throw an error, but use fallback behavior
			const result = getPeriodNamings(periods, "invalid-lang", "standard");
			expect(result).toHaveLength(1);
			expect(result[0].label).toBeDefined();
		});

		test("should handle leap year February correctly", () => {
			const periods = [
				{ from: "2024-02-01", to: "2024-02-29" }, // Leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});

		test("should handle non-leap year February correctly", () => {
			const periods = [
				{ from: "2023-02-01", to: "2023-02-28" }, // Non-leap year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-02-01–2023-02-28",
					from: "2023-02-01",
					to: "2023-02-28",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});
	});

	describe("Period type detection", () => {
		test("should not treat partial month as calendar month", () => {
			const periods = [
				{ from: "2024-01-02", to: "2024-01-31" }, // Not starting from 1st
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("latest");
		});

		test("should not treat partial month end as calendar month", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-30" }, // Not ending on last day
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should be treated as same-length period, not calendar month
			expect(result[0].labelDesc).toBeDefined(); // Should have period description
			expect(result[0].textName).toContain("latest");
		});

		test("should show years when 11+ months difference", () => {
			const periods = [
				{ from: "2023-01-01", to: "2023-01-07" }, // 12 months before latest (should show year)
				{ from: "2023-03-01", to: "2023-03-07" }, // 10 months before latest (should not show year)
				{ from: "2024-01-01", to: "2024-01-07" }, // Latest year
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2023-01-01–2023-01-07",
					from: "2023-01-01",
					to: "2023-01-07",
					label: "Jan 1–Jan 7, 2023", // Shows year once (12 months difference >= 11, same year)
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7, 2023",
				},
				{
					code: "2023-03-01–2023-03-07",
					from: "2023-03-01",
					to: "2023-03-07",
					label: "Mar 1–Mar 7", // No year (10 months difference < 11)
					labelDesc: "1-week periods",
					textName: "1-week period ending March 7",
				},
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 1–Jan 7", // No year (latest)
					labelDesc: "1-week periods",
					textName: "latest 1-week period",
				},
			]);
		});

		test("should show years separately when dates span different years", () => {
			const periods = [
				{ from: "2022-12-26", to: "2023-01-01" }, // Cross-year period, 12+ months difference from latest
				{ from: "2024-01-01", to: "2024-01-07" }, // Latest period
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2022-12-26–2023-01-01",
					from: "2022-12-26",
					to: "2023-01-01",
					label: "Dec 26, 2022–Jan 1, 2023", // Shows years separately (different years)
					labelDesc: "1-week periods",
					textName: "1-week period ending January 1, 2023",
				},
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 1–Jan 7", // No year (latest)
					labelDesc: "1-week periods",
					textName: "latest 1-week period",
				},
			]);
		});
	});

	describe("Period ordering", () => {
		test("should correctly identify latest period when periods are in reverse order (newest to oldest)", () => {
			const periods = [
				{ from: "2024-01-15", to: "2024-01-21" }, // Latest period (newest)
				{ from: "2024-01-08", to: "2024-01-14" }, // Middle period
				{ from: "2024-01-01", to: "2024-01-07" }, // Oldest period
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 15–Jan 21",
					labelDesc: "1-week periods",
					textName: "latest 1-week period", // Should be marked as latest
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 8–Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14", // Should NOT be marked as latest
				},
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 1–Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7", // Should NOT be marked as latest
				},
			]);
		});

		test("should correctly identify latest period when periods are in chronological order (oldest to newest)", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // Oldest period
				{ from: "2024-01-08", to: "2024-01-14" }, // Middle period
				{ from: "2024-01-15", to: "2024-01-21" }, // Latest period (newest)
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "Jan 1–Jan 7",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 7", // Should NOT be marked as latest
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "Jan 8–Jan 14",
					labelDesc: "1-week periods",
					textName: "1-week period ending January 14", // Should NOT be marked as latest
				},
				{
					code: "2024-01-15–2024-01-21",
					from: "2024-01-15",
					to: "2024-01-21",
					label: "Jan 15–Jan 21",
					labelDesc: "1-week periods",
					textName: "latest 1-week period", // Should be marked as latest
				},
			]);
		});
	});

	describe("Mixed period types", () => {
		test("should handle mixed period types with multiple latest periods", () => {
			// Example from user: mix of monthly, weekly, and quarterly periods
			const periods = [
				{ from: "2025-05-05", to: "2025-06-01" }, // 28-day period
				{ from: "2025-04-07", to: "2025-05-04" }, // 28-day period
				{ from: "2025-03-10", to: "2025-04-06" }, // 28-day period
				{ from: "2025-02-10", to: "2025-03-09" }, // 28-day period
				{ from: "2025-05-26", to: "2025-06-01" }, // 7-day period (latest overall)
				{ from: "2025-05-19", to: "2025-05-25" }, // 7-day period
				{ from: "2025-05-12", to: "2025-05-18" }, // 7-day period
				{ from: "2025-05-05", to: "2025-05-11" }, // 7-day period
				{ from: "2025-03-01", to: "2025-05-31" }, // 92-day period (quarterly)
				{ from: "2024-12-01", to: "2025-02-28" }, // 90-day period (quarterly)
				{ from: "2024-09-01", to: "2024-11-30" }, // 91-day period (quarterly)
				{ from: "2024-06-01", to: "2024-08-31" }, // 92-day period (quarterly)
				{ from: "2024-03-01", to: "2024-05-31" }, // 92-day period (quarterly)
			];

			const result = getPeriodNamings(periods, "en", "standard");

			// Should have 13 results, one for each period
			expect(result).toHaveLength(13);

			// Check that we have multiple "latest" periods - one for each period type
			// Note: Since quarterly periods have different lengths (90, 91, 92 days),
			// they are treated as separate period types, so we expect only 2 "latest" periods:
			// - One for 7-day periods (latest overall)
			// - One for 28-day periods (also ends on latest date)
			const latestPeriods = result.filter((r) => r.textName.includes("latest"));
			expect(latestPeriods).toHaveLength(2); // Only periods ending on the overall latest date

			// The 1-week period ending 2025-06-01 should be latest (overall latest date)
			const weeklyLatest = result.find((r) => r.from === "2025-05-26" && r.to === "2025-06-01" && r.textName.includes("latest"));
			expect(weeklyLatest).toBeDefined();
			expect(weeklyLatest?.textName).toBe("latest 1-week period");

			// The 28-day period ending 2025-06-01 should be latest for its type
			const monthlyLatest = result.find((r) => r.from === "2025-05-05" && r.to === "2025-06-01" && r.textName.includes("latest"));
			expect(monthlyLatest).toBeDefined();
			expect(monthlyLatest?.textName).toBe("latest 4-week period");

			// The quarterly periods should not be marked as "latest" since their latest dates
			// are not the overall latest date (2025-05-31 vs 2025-06-01)
			const quarterlyPeriods = result.filter(
				(r) => r.textName.includes("13-week") || r.textName.includes("90-day") || r.textName.includes("91-day"),
			);
			quarterlyPeriods.forEach((period) => {
				expect(period.textName).not.toContain("latest");
				expect(period.textName).toContain("ending");
			});
		});

		test("should handle mixed periods where some types don't have latest period", () => {
			const periods = [
				{ from: "2025-05-26", to: "2025-06-01" }, // 1-week period (latest overall)
				{ from: "2025-05-19", to: "2025-05-25" }, // 1-week period
				{ from: "2025-04-01", to: "2025-04-28" }, // 4-week period (not latest overall)
				{ from: "2025-03-01", to: "2025-03-28" }, // 4-week period
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toHaveLength(4);

			// Only the 1-week period should have "latest" since it has the overall latest date
			const latestPeriods = result.filter((r) => r.textName.includes("latest"));
			expect(latestPeriods).toHaveLength(1);

			const weeklyLatest = latestPeriods[0];
			expect(weeklyLatest.from).toBe("2025-05-26");
			expect(weeklyLatest.to).toBe("2025-06-01");
			expect(weeklyLatest.textName).toBe("latest 1-week period");

			// The 4-week periods should not have "latest" since their latest date is not the overall latest
			const fourWeekPeriods = result.filter((r) => r.textName.includes("4-week"));
			fourWeekPeriods.forEach((period) => {
				expect(period.textName).not.toContain("latest");
				expect(period.textName).toContain("ending");
			});
		});

		test("should handle mixed calendar months and same-length periods", () => {
			const periods = [
				{ from: "2025-01-01", to: "2025-01-31" }, // Calendar month
				{ from: "2025-02-01", to: "2025-02-28" }, // Calendar month
				{ from: "2025-03-01", to: "2025-03-07" }, // 1-week period (latest overall)
				{ from: "2025-02-22", to: "2025-02-28" }, // 1-week period
			];

			const result = getPeriodNamings(periods, "en", "standard");

			expect(result).toHaveLength(4);

			// Calendar months should use month names
			const calendarMonths = result.filter((r) => r.labelDesc === undefined);
			expect(calendarMonths).toHaveLength(2);
			expect(calendarMonths[0].label).toBe("Jan");
			expect(calendarMonths[0].textName).toBe("January");
			expect(calendarMonths[1].label).toBe("Feb");
			expect(calendarMonths[1].textName).toBe("February");

			// 1-week periods should have period descriptions
			const weeklyPeriods = result.filter((r) => r.labelDesc === "1-week periods");
			expect(weeklyPeriods).toHaveLength(2);

			// Only the 1-week period with latest date should be marked as latest
			const latestPeriods = result.filter((r) => r.textName.includes("latest"));
			expect(latestPeriods).toHaveLength(1);
			expect(latestPeriods[0].from).toBe("2025-03-01");
			expect(latestPeriods[0].to).toBe("2025-03-07");
		});
	});

	describe("Thousand-days calendar format (dateFormat: 't')", () => {
		test("should use moment format 't' for thousand-days calendar", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-07" }, // 7 days
				{ from: "2024-01-08", to: "2024-01-14" }, // 7 days
			];

			const result = getPeriodNamings(periods, "en", "t");

			// The 't' format produces thousand-days calendar dates like "19-095"
			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-07",
					from: "2024-01-01",
					to: "2024-01-07",
					label: "19-089–19-095",
					labelDesc: "1-week periods",
					textName: "1-week period ending 19-095",
				},
				{
					code: "2024-01-08–2024-01-14",
					from: "2024-01-08",
					to: "2024-01-14",
					label: "19-096–19-102",
					labelDesc: "1-week periods",
					textName: "latest 1-week period",
				},
			]);
		});

		test("should use moment format 't' for calendar months with thousand-days format", () => {
			const periods = [
				{ from: "2024-01-01", to: "2024-01-31" },
				{ from: "2024-02-01", to: "2024-02-29" },
			];

			const result = getPeriodNamings(periods, "en", "t");

			// For calendar months, the thousand-days format doesn't apply to month names
			// Only to date formatting in Type C periods
			expect(result).toEqual([
				{
					code: "2024-01-01–2024-01-31",
					from: "2024-01-01",
					to: "2024-01-31",
					label: "Jan",
					labelDesc: undefined,
					textName: "January",
				},
				{
					code: "2024-02-01–2024-02-29",
					from: "2024-02-01",
					to: "2024-02-29",
					label: "Feb",
					labelDesc: undefined,
					textName: "February",
				},
			]);
		});
	});
});
