CHATGPT4O_TPM=1000000
LOCAL_REDIS_HOST='Use await import to utilize test containers'
LOCAL_REDIS_PORT='Use await import to utilize test containers'
PG_HOST='Use await import to utilize test containers'
PG_PORT='Use await import to utilize test containers'
PG_USER='Use await import to utilize test containers'
PG_PASSWORD='Use await import to utilize test containers'
PG_DATABASE='Use await import to utilize test containers'
WORKER_DIR=/tmp/worker
JWT_SECRET=3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R
TEMPORAL_ADDRESS='Use await import to utilize test containers'
TEMPORAL_JWT_TOKEN='Use await import to utilize test containers'
# openai project development
OPENAI_API_KEY=********************************************************************************************************************************************************************
# test assistant
OPENAI_ASSISTANT_ID=asst_Tik9SRUkbNbIwyWlihCeyiXh