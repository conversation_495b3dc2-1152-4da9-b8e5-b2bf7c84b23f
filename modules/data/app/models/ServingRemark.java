package models;

import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

import jakarta.persistence.*;
import javax.validation.constraints.Size;

@Entity(name = "ServingRemark")
@Table(name="servingremarks")
@NamedNativeQueries({
    @NamedNativeQuery(
        name = "servingremarks.all",
        query = "select a.code, a.description, a.external_code, " +
            "cast(case when not exists (select d.code from servingremarks_disabled d where d.code = a.code) then 0 else 1 end as boolean) as disabled " +
            "from servingremarks_all a order by a.code",
        hints = {@QueryHint(name= QueryHints.CURSOR, value= HintValues.TRUE)}),
    @NamedNativeQuery(
        name = "servingremarks.updated.after",
        query = "select a.code, a.description, a.external_code, " +
            "cast(case when not exists (select d.code from servingremarks_disabled d where d.code = a.code) then 0 else 1 end as boolean) as disabled " +
            "from servingremarks_all a where a.update_date > ?",
        hints = {@QueryHint(name= QueryHints.CURSOR, value= HintValues.TRUE)}),
    @NamedNativeQuery(
        name = "servingremarks.deleted.after",
        query = "select code from servingremarks_all_deleted where update_date > ?",
        hints = {@QueryHint(name=QueryHints.CURSOR, value= HintValues.TRUE)})
})

public class ServingRemark extends TranslatedCodeTable<ServingRemark> implements WithCodeDescription {

    private String description;
    private String externalCode;
    private ServingRemarkLang translated;

    @OneToOne(mappedBy = "parent", fetch = FetchType.LAZY, optional = true, cascade = CascadeType.ALL)
    public ServingRemarkLang getTranslated() {
        return translated;
    }

    public void setTranslated(ServingRemarkLang translated) {
        this.translated = translated;
    }

    @Size(max = 150)
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "external_code")
    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    @Override
    public String codeDescription() {
        return getDescription();
    }
}
