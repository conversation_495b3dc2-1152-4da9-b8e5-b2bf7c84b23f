package models.handlers

import com.cloudfarms.pigs.sync.events.{ AnimalRenamedDTO, EventReader, PurchaseCreatedDTO, YoungAnimalPurchaseDTO }
import models.{ BaseWithId, Boar, BreedingAnimal, Location, Sow, TransferIn, TransferIndividualIn }

import java.sql.Connection
import javax.inject.{ Inject, Singleton }
import scalikejdbc._

import java.util.Date
import scala.jdk.CollectionConverters._

@Singleton
class PurchaseHandler @Inject() (
  problematicActionsHandler: ProblematicActionsHandler,
  individualAnimalHandler: IndividualAnimalHandler,
) {

  def processPurchaseCreated(uow: UnitOfWork, dto: PurchaseCreatedDTO): Unit = {
    val db = {
      val connection = uow.em.unwrap(classOf[Connection])
      DB(connection).autoClose(false)
    }
    db.withinTx { implicit dbSession =>
      if (dto.getBusinessId != null) {
        val bizExists = sql"select count(*) from business where id = ${dto.getBusinessId}".map(_.int(1)).single.apply().get == 1
        if (!bizExists) {
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_PURCHASE_CREATED,
            dto,
            "error.company.not.found",
            dto.getBusinessId,
          )
        }
      }
      if (dto.getId < 0) {
        val createdId = {
          sql"""insert into transferin(transfername, actor_date, business_id, comment)
               |values (${dto.getTransferName}, ${dto.getWhen}, ${dto.getBusinessId}, ${dto.getComment})
               |returning id
               |""".stripMargin.map(_.long(1)).single.apply().get
        }
        uow.transferIns.put(dto.getId, uow.em.find(classOf[TransferIn], createdId))
      } else {
        val updatedCount = {
          sql"""update transferin set (transfername, actor_date, business_id, comment) =
               |(${dto.getTransferName}, ${dto.getWhen}, ${dto.getBusinessId}, ${dto.getComment})
               |where id = ${dto.getId}

               |""".stripMargin.executeUpdate.apply()
        }
        if (updatedCount != 1) {
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_PURCHASE_CREATED,
            dto,
            "error.purchase.not.found",
            java.lang.Long.valueOf(dto.getId),
          )
        }
      }
    }
  }

  def processYoungAnimalPurchase(uow: UnitOfWork, dto: YoungAnimalPurchaseDTO): Unit = {
    val db = {
      val connection = uow.em.unwrap(classOf[Connection])
      DB(connection).autoClose(false)
    }

    val transferIn = uow.transferIns.get(dto.getTransferInId)
    val transferInId: Long = Option(transferIn) match {
      case None    => dto.getTransferInId
      case Some(t) => t.getId
    }
    dto.setTransferInId(transferInId)

    if (uow.em.find(classOf[TransferIn], transferInId) == null) {
      problematicActionsHandler.throwProblem(
        uow.fileId,
        uow.messages,
        EventReader.REPORTING_YBA_PURCHASED,
        dto,
        "error.purchase.not.found",
        java.lang.Long.valueOf(dto.getTransferInId),
      )
    }

    db.withinTx { implicit dbSession =>
      Option(dto.getAnimalId) foreach { animalId =>
        val existingYbaOpt = Option(individualAnimalHandler.breedingAnimalByAnimalIdAtOrLater(uow.em, animalId, dto.getWhen))
        existingYbaOpt.orElse(Option(individualAnimalHandler.sowByAnimalIdAtOrLater(uow.em, animalId, dto.getWhen))).foreach { _ =>
          if (dto.getId < 0) uow.transferIndividualIns.put(dto.getId, null)
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_YBA_PURCHASED,
            dto,
            "error.animalid.exists",
            animalId,
            dto.getWhen,
          )
        }
      }
      Option(dto.getAnimalName) foreach { animalName =>
        val existingYbaOpt = Option(individualAnimalHandler.breedingAnimalByAnimalNumberOrLater(
          uow.em,
          animalName,
          dto.getWhen,
          if (dto.isFemale) "female" else "male",
        ))
        existingYbaOpt.orElse(Option(individualAnimalHandler.sowBySowNumberAliveAfter(uow.em, animalName, dto.getWhen))).foreach { _ =>
          if (dto.getId < 0) uow.transferIndividualIns.put(dto.getId, null)
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_YBA_PURCHASED,
            dto,
            "error.sownumber.exists",
            animalName,
            dto.getWhen,
          )
        }
      }

      val loc = uow.em.find(classOf[Location], dto.getLocationId)
      if (loc == null) {
        if (dto.getId < 0) uow.transferIndividualIns.put(dto.getId, null)
        problematicActionsHandler.throwProblem(
          uow.fileId,
          uow.messages,
          EventReader.REPORTING_YBA_PURCHASED,
          dto,
          "error.location.not.found",
          dto.getLocationId,
        )
      }
      if (
        loc.getInhabitanttype_code != "GILT" &&
        (loc.getInhabitanttype_code != "SOW" || !dto.isFemale)
        || (loc.getInhabitanttype_code == "GILT" && dto.getServingDate != null)
      ) {
        if (dto.getId < 0) uow.transferIndividualIns.put(dto.getId, null)
        problematicActionsHandler.throwProblem(
          uow.fileId,
          uow.messages,
          EventReader.REPORTING_YBA_PURCHASED,
          dto,
          "js.label.timeline.error.locationnotsuitable",
          dto.getLocationId,
        )
      }
      // TODO: Check whether animals with the RFIDs already exist. If so, reject
      val transferIndividualInId: Long = {
        if (dto.getServingDate == null) {
          sql"""
             |with x(sex, sownumber, animalid, breed, birthdate) as (values(
             |    ${if (dto.isFemale) "female" else "male"},
             |    ${dto.getAnimalName},
             |    ${dto.getAnimalId},
             |    ${dto.getBreed},
             |    ${dto.getBirthDate}::date)
             |), g as (
             |  insert into gilt(sex, sownumber, animalid, breed, birthdate, location_id, created_location_id, rfidlf, rfiduhf, giltdate)
             |  select
             |    x.sex, x.sownumber, x.animalid,
             |    coalesce(x.breed, b.breed),
             |    coalesce(x.birthdate, b.birthdate),
             |    ${dto.getLocationId},
             |    ${dto.getLocationId},
             |    ${dto.getRfidLf},
             |    ${dto.getRfidUhf},
             |    ${transferIn.getActorDate}
             |  from x left join breed b on b.animalid = x.animalid
             |  returning id
             |) insert into transferindividualin (transferin_id, gilt_id, location_id, price, pigliveweight)
             |  select $transferInId, g.id, ${dto.getLocationId}, ${dto.getPrice}, ${dto.getPigLiveWeight} from g
             |  returning id
             |""".stripMargin.map(_.long("id")).single.apply().get
        } else if (dto.isFemale) {
          sql"""
             |with x(servingdate, sownumber, animalid, breed, birthdate) as (values(
             |    ${dto.getServingDate}::timestamptz,
             |    ${dto.getAnimalName},
             |    ${dto.getAnimalId},
             |    ${dto.getBreed},
             |    ${dto.getBirthDate}::date)
             |), g as (
             |  insert into sow(sowdate, sownumber, animalid, breed, birthdate, location_id, created_location_id, rfidlf, rfiduhf, entrydate)
             |  select
             |    x.servingdate, x.sownumber, x.animalid,
             |    coalesce(x.breed, b.breed),
             |    coalesce(x.birthdate, b.birthdate),
             |    ${dto.getLocationId},
             |    ${dto.getLocationId},
             |    ${dto.getRfidLf},
             |    ${dto.getRfidUhf},
             |    ${transferIn.getActorDate}
             |  from x left join breed b on b.animalid = x.animalid
             |  returning id, sowdate
             |), tii as (insert into transferindividualin (transferin_id, sow_id, location_id, price, pigliveweight)
             |  select $transferInId, g.id, ${dto.getLocationId}, ${dto.getPrice}, ${dto.getPigLiveWeight} from g returning id
             |), i as (
             |  insert into serving(sow_id, actor_date)
             |  select g.id, g.sowdate
             |  from g
             |  returning id, actor_date
             |), sb0 as (
             |  select id from semenbatch where semenbatchnumber = '?' and actor_date <= ${dto.getServingDate}::timestamptz and (usable_until is null or usable_until >${dto.getServingDate}::timestamptz)
             |), sb1 as (
             |  insert into semenbatch (semenbatchnumber, actor_date, usable_until)
             |  select '?',
             |  ((${dto.getServingDate}::timestamptz at time zone o.timezonename)::date + '0:0'::time) at time zone o.timezonename,
             |  ((${dto.getServingDate}::timestamptz at time zone o.timezonename)::date + 1 + '0:0'::time) at time zone o.timezonename
             |  from organization o where o.id = get_tenant_organization()
             |  and not exists (select 1 from sb0)
             |  returning id, actor_date
             |), sb as (
             |  select id from sb0 union all select id from sb1
             |), smn as (
             |  insert into semen(semenbatch_id, actor_date) select id, actor_date from sb1
             |), se as (
             |insert into servingevent(serving_id, actor_date, semenbatch_id)
             |  select i.id, i.actor_date, sb.id
             |  from i cross join sb
             |) select id from tii;
             |""".stripMargin.map(_.long("id")).single.apply().get
        } else {
          if (dto.getId < 0) uow.transferIndividualIns.put(dto.getId, null)
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_YBA_PURCHASED,
            dto,
            "js.label.timeline.error.inseminated_male",
          ).asInstanceOf[Nothing]
        }
      }
      uow.transferIndividualIns.put(dto.getId, uow.em.find(classOf[TransferIndividualIn], transferIndividualInId))
      if (dto.getAnimalId != null && (dto.getSireId != null || dto.getDamId != null || dto.getIndex != null)) {
        sql"update breed set sireid = coalesce(${dto.getSireId},sireid), damid = coalesce(${dto.getDamId},damid), index=coalesce(${dto.getIndex}, index) where animalid = ${dto.getAnimalId}".executeUpdate.apply()
      }
    }
  }
  def processAnimalRenamed(uow: UnitOfWork, dto: AnimalRenamedDTO): Unit = {
    val db = {
      val connection = uow.em.unwrap(classOf[Connection])
      DB(connection).autoClose(false)
    }

    db.withinTx {
      def checkWhetherFoundAndRename[A <: BaseWithId](animal: A)(getAnimalId: => String)(setNumber: String => Unit)(
        otherAnimalExists: => Boolean,
      ): Unit = {
        if (animal == null) {
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_ANIMAL_RENAMED,
            dto,
            "js.error.animal.not_found",
          )
        }
        if (getAnimalId != null && dto.getAnimalId != null && dto.getAnimalId != getAnimalId) {
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_ANIMAL_RENAMED,
            dto,
            "error.mismatch.animalid",
            dto.getAnimalId,
            getAnimalId,
          )
        }
        if (otherAnimalExists) {
          problematicActionsHandler.throwProblem(
            uow.fileId,
            uow.messages,
            EventReader.REPORTING_ANIMAL_RENAMED,
            dto,
            "error.animalnumber.exists",
            dto.getAnimalName,
            dto.getWhen,
          )
        }
        setNumber(dto.getAnimalName)
      }

      implicit dbSession =>
        Option(dto.getSowId) match {
          case Some(sowId) =>
            val sow = uow.sows.asScala.getOrElse(sowId, uow.em.find(classOf[Sow], sowId))
            if (sow != null) uow.em.refresh(sow)
            checkWhetherFoundAndRename(sow)(sow.getAnimalId)(sow.setSowNumber) {
              individualAnimalHandler.sowBySowNumberAliveIn(uow.em, dto.getAnimalName, sow.getEntryDate, sow.getExitDate) != null ||
              individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                uow.em,
                dto.getAnimalName,
                sow.getEntryDate,
                sow.getExitDate,
                "female",
              ) != null
            }
          case None =>
            Option(dto.getBoarId) match {
              case Some(boarId) =>
                val boar = uow.boars.asScala.getOrElse(boarId, uow.em.find(classOf[Boar], boarId))
                if (boar != null) uow.em.refresh(boar)
                checkWhetherFoundAndRename(boar)(boar.getAnimalId)(boar.setBoarNumber) {
                  val exitDate: Date = if (boar.getDeath != null) boar.getDeath.getActorDate else null
                  individualAnimalHandler.boarByBoarNumberAliveIn(uow.em, dto.getAnimalName, boar.getEntryDate, exitDate) != null ||
                  individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                    uow.em,
                    dto.getAnimalName,
                    boar.getEntryDate,
                    exitDate,
                    "female",
                  ) != null
                }
              case None =>
                Option(dto.getGiltId) match {
                  case Some(giltId) =>
                    val gilt = uow.gilts.asScala.getOrElse(giltId, uow.em.find(classOf[BreedingAnimal], giltId))
                    if (gilt != null) uow.em.refresh(gilt)
                    checkWhetherFoundAndRename(gilt)(gilt.getAnimalId)(gilt.setSowNumber) {
                      if ("female" == gilt.getSex) {
                        individualAnimalHandler.sowBySowNumberAliveIn(
                          uow.em,
                          dto.getAnimalName,
                          gilt.getEntryDate,
                          gilt.getExitDate,
                        ) != null ||
                        individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                          uow.em,
                          dto.getAnimalName,
                          gilt.getEntryDate,
                          gilt.getExitDate,
                          "female",
                        ) != null
                      } else {
                        individualAnimalHandler.boarByBoarNumberAliveIn(
                          uow.em,
                          dto.getAnimalName,
                          gilt.getEntryDate,
                          gilt.getExitDate,
                        ) != null ||
                        individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                          uow.em,
                          dto.getAnimalName,
                          gilt.getEntryDate,
                          gilt.getExitDate,
                          "female",
                        ) != null
                      }
                    }
                  case None =>
                    Option(dto.getAnimalId) match {
                      case Some(animalId) =>
                        Option(individualAnimalHandler.breedingAnimalByAnimalId(uow.em, animalId, dto.getWhen, false)) match {
                          case Some(gilt) => checkWhetherFoundAndRename(gilt)(gilt.getAnimalId)(gilt.setSowNumber) {
                              if ("female" == gilt.getSex) {
                                individualAnimalHandler.sowBySowNumberAliveIn(
                                  uow.em,
                                  dto.getAnimalName,
                                  gilt.getEntryDate,
                                  gilt.getExitDate,
                                ) != null ||
                                individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                                  uow.em,
                                  dto.getAnimalName,
                                  gilt.getEntryDate,
                                  gilt.getExitDate,
                                  "female",
                                ) != null
                              } else {
                                individualAnimalHandler.boarByBoarNumberAliveIn(
                                  uow.em,
                                  dto.getAnimalName,
                                  gilt.getEntryDate,
                                  gilt.getExitDate,
                                ) != null ||
                                individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                                  uow.em,
                                  dto.getAnimalName,
                                  gilt.getEntryDate,
                                  gilt.getExitDate,
                                  "female",
                                ) != null
                              }
                            }
                          case None =>
                            Option(individualAnimalHandler.sowByAnimalId(uow.em, animalId, dto.getWhen)) match {
                              case Some(sow) => checkWhetherFoundAndRename(sow)(sow.getAnimalId)(sow.setSowNumber) {
                                  individualAnimalHandler.sowBySowNumberAliveIn(
                                    uow.em,
                                    dto.getAnimalName,
                                    sow.getEntryDate,
                                    sow.getExitDate,
                                  ) != null ||
                                  individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                                    uow.em,
                                    dto.getAnimalName,
                                    sow.getEntryDate,
                                    sow.getExitDate,
                                    "female",
                                  ) != null
                                }
                              case None => Option(individualAnimalHandler.boarByAnimalId(uow.em, animalId)) match {
                                  case Some(boar) => checkWhetherFoundAndRename(boar)(boar.getAnimalId)(boar.setBoarNumber) {
                                      val exitDate: Date = if (boar.getDeath != null) boar.getDeath.getActorDate else null
                                      individualAnimalHandler.boarByBoarNumberAliveIn(
                                        uow.em,
                                        dto.getAnimalName,
                                        boar.getEntryDate,
                                        exitDate,
                                      ) != null ||
                                      individualAnimalHandler.breedingAnimalBySowNumberAliveIn(
                                        uow.em,
                                        dto.getAnimalName,
                                        boar.getEntryDate,
                                        exitDate,
                                        "female",
                                      ) != null
                                    }
                                  case None => problematicActionsHandler.throwProblem(
                                      uow.fileId,
                                      uow.messages,
                                      EventReader.REPORTING_ANIMAL_RENAMED,
                                      dto,
                                      "js.error.animal.not_found",
                                    )
                                }
                            }
                        }

                      case None => // Nothing to do
                    }
                }
            }
        }
    }
  }

}
