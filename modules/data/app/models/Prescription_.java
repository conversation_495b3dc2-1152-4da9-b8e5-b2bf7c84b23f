package models;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.annotation.Generated;
import jakarta.persistence.metamodel.ListAttribute;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(Prescription.class)
public abstract class Prescription_ extends models.BaseWithId_ {

	public static volatile SingularAttribute<Prescription, String> medApplicationCode;
	public static volatile SingularAttribute<Prescription, Timestamp> updateDate;
	public static volatile SingularAttribute<Prescription, Date> actorDate;
	public static volatile SingularAttribute<Prescription, Integer> packagesCount;
	public static volatile SingularAttribute<Prescription, Long> medicineId;
	public static volatile ListAttribute<Prescription, Cure> cures;
	public static volatile SingularAttribute<Prescription, FarmMedicine> medicine;
	public static volatile SingularAttribute<Prescription, BigDecimal> packageSize;
	public static volatile SingularAttribute<Prescription, String> unit;
	public static volatile SingularAttribute<Prescription, Long> actorId;
	public static volatile SingularAttribute<Prescription, String> name;
	public static volatile SingularAttribute<Prescription, String> comment;
	public static volatile SingularAttribute<Prescription, Date> expirationDate;
	public static volatile SingularAttribute<Prescription, Short> withdrawalDays;

}

