/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables.records;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SystemPortletSettingsDeletedRecord extends org.jooq.impl.UpdatableRecordImpl<jooq.public_.tables.records.SystemPortletSettingsDeletedRecord> implements org.jooq.Record4<java.lang.Long, java.sql.Timestamp, java.lang.Long, java.lang.String> {

	private static final long serialVersionUID = 51432759;

	/**
	 * Setter for <code>public.system_portlet_settings_deleted.id</code>.
	 */
	public void setId(java.lang.Long value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>public.system_portlet_settings_deleted.id</code>.
	 */
	public java.lang.Long getId() {
		return (java.lang.Long) getValue(0);
	}

	/**
	 * Setter for <code>public.system_portlet_settings_deleted.update_date</code>.
	 */
	public void setUpdateDate(java.sql.Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>public.system_portlet_settings_deleted.update_date</code>.
	 */
	public java.sql.Timestamp getUpdateDate() {
		return (java.sql.Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>public.system_portlet_settings_deleted.update_account_id</code>.
	 */
	public void setUpdateAccountId(java.lang.Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>public.system_portlet_settings_deleted.update_account_id</code>.
	 */
	public java.lang.Long getUpdateAccountId() {
		return (java.lang.Long) getValue(2);
	}

	/**
	 * Setter for <code>public.system_portlet_settings_deleted.update_ui</code>.
	 */
	public void setUpdateUi(java.lang.String value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>public.system_portlet_settings_deleted.update_ui</code>.
	 */
	public java.lang.String getUpdateUi() {
		return (java.lang.String) getValue(3);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Record1<java.lang.Long> key() {
		return (org.jooq.Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record4 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row4<java.lang.Long, java.sql.Timestamp, java.lang.Long, java.lang.String> fieldsRow() {
		return (org.jooq.Row4) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row4<java.lang.Long, java.sql.Timestamp, java.lang.Long, java.lang.String> valuesRow() {
		return (org.jooq.Row4) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.Long> field1() {
		return jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.sql.Timestamp> field2() {
		return jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.Long> field3() {
		return jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.String> field4() {
		return jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.Long value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.sql.Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.Long value3() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.String value4() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SystemPortletSettingsDeletedRecord value1(java.lang.Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SystemPortletSettingsDeletedRecord value2(java.sql.Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SystemPortletSettingsDeletedRecord value3(java.lang.Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SystemPortletSettingsDeletedRecord value4(java.lang.String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SystemPortletSettingsDeletedRecord values(java.lang.Long value1, java.sql.Timestamp value2, java.lang.Long value3, java.lang.String value4) {
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached SystemPortletSettingsDeletedRecord
	 */
	public SystemPortletSettingsDeletedRecord() {
		super(jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED);
	}

	/**
	 * Create a detached, initialised SystemPortletSettingsDeletedRecord
	 */
	public SystemPortletSettingsDeletedRecord(java.lang.Long id, java.sql.Timestamp updateDate, java.lang.Long updateAccountId, java.lang.String updateUi) {
		super(jooq.public_.tables.SystemPortletSettingsDeleted.SYSTEM_PORTLET_SETTINGS_DELETED);

		setValue(0, id);
		setValue(1, updateDate);
		setValue(2, updateAccountId);
		setValue(3, updateUi);
	}
}
