/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables.records;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EcbXchangeRateRecord extends org.jooq.impl.UpdatableRecordImpl<jooq.public_.tables.records.EcbXchangeRateRecord> implements org.jooq.Record3<java.sql.Date, java.lang.String, java.math.BigDecimal> {

	private static final long serialVersionUID = 1270540012;

	/**
	 * Setter for <code>public.ecb_xchange_rate.day</code>.
	 */
	public void setDay(java.sql.Date value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>public.ecb_xchange_rate.day</code>.
	 */
	public java.sql.Date getDay() {
		return (java.sql.Date) getValue(0);
	}

	/**
	 * Setter for <code>public.ecb_xchange_rate.currency_id</code>.
	 */
	public void setCurrencyId(java.lang.String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>public.ecb_xchange_rate.currency_id</code>.
	 */
	public java.lang.String getCurrencyId() {
		return (java.lang.String) getValue(1);
	}

	/**
	 * Setter for <code>public.ecb_xchange_rate.rate</code>.
	 */
	public void setRate(java.math.BigDecimal value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>public.ecb_xchange_rate.rate</code>.
	 */
	public java.math.BigDecimal getRate() {
		return (java.math.BigDecimal) getValue(2);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Record2<java.sql.Date, java.lang.String> key() {
		return (org.jooq.Record2) super.key();
	}

	// -------------------------------------------------------------------------
	// Record3 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row3<java.sql.Date, java.lang.String, java.math.BigDecimal> fieldsRow() {
		return (org.jooq.Row3) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row3<java.sql.Date, java.lang.String, java.math.BigDecimal> valuesRow() {
		return (org.jooq.Row3) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.sql.Date> field1() {
		return jooq.public_.tables.EcbXchangeRate.ECB_XCHANGE_RATE.DAY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.String> field2() {
		return jooq.public_.tables.EcbXchangeRate.ECB_XCHANGE_RATE.CURRENCY_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.math.BigDecimal> field3() {
		return jooq.public_.tables.EcbXchangeRate.ECB_XCHANGE_RATE.RATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.sql.Date value1() {
		return getDay();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.String value2() {
		return getCurrencyId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.math.BigDecimal value3() {
		return getRate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EcbXchangeRateRecord value1(java.sql.Date value) {
		setDay(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EcbXchangeRateRecord value2(java.lang.String value) {
		setCurrencyId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EcbXchangeRateRecord value3(java.math.BigDecimal value) {
		setRate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EcbXchangeRateRecord values(java.sql.Date value1, java.lang.String value2, java.math.BigDecimal value3) {
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached EcbXchangeRateRecord
	 */
	public EcbXchangeRateRecord() {
		super(jooq.public_.tables.EcbXchangeRate.ECB_XCHANGE_RATE);
	}

	/**
	 * Create a detached, initialised EcbXchangeRateRecord
	 */
	public EcbXchangeRateRecord(java.sql.Date day, java.lang.String currencyId, java.math.BigDecimal rate) {
		super(jooq.public_.tables.EcbXchangeRate.ECB_XCHANGE_RATE);

		setValue(0, day);
		setValue(1, currencyId);
		setValue(2, rate);
	}
}
