/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.tables.records.SparePartCategoryTypeRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SparePartCategoryType extends TableImpl<SparePartCategoryTypeRecord> {

	private static final long serialVersionUID = -768314376;

	/**
	 * The reference instance of <code>farm.spare_part_category_type</code>
	 */
	public static final SparePartCategoryType SPARE_PART_CATEGORY_TYPE = new SparePartCategoryType();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<SparePartCategoryTypeRecord> getRecordType() {
		return SparePartCategoryTypeRecord.class;
	}

	/**
	 * The column <code>farm.spare_part_category_type.code</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.spare_part_category_type.name</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>farm.spare_part_category_type.create_date</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.spare_part_category_type.update_date</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.spare_part_category_type.create_account_id</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.spare_part_category_type.update_account_id</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.spare_part_category_type.create_ui</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1), this, "");

	/**
	 * The column <code>farm.spare_part_category_type.update_ui</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1), this, "");

	/**
	 * The column <code>farm.spare_part_category_type.disabled</code>.
	 */
	public final TableField<SparePartCategoryTypeRecord, Boolean> DISABLED = createField("disabled", org.jooq.impl.SQLDataType.BOOLEAN, this, "");

	/**
	 * Create a <code>farm.spare_part_category_type</code> table reference
	 */
	public SparePartCategoryType() {
		this("spare_part_category_type", null);
	}

	/**
	 * Create an aliased <code>farm.spare_part_category_type</code> table reference
	 */
	public SparePartCategoryType(String alias) {
		this(alias, SPARE_PART_CATEGORY_TYPE);
	}

	private SparePartCategoryType(String alias, Table<SparePartCategoryTypeRecord> aliased) {
		this(alias, aliased, null);
	}

	private SparePartCategoryType(String alias, Table<SparePartCategoryTypeRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SparePartCategoryType as(String alias) {
		return new SparePartCategoryType(alias, this);
	}

	/**
	 * Rename this table
	 */
	public SparePartCategoryType rename(String name) {
		return new SparePartCategoryType(name, null);
	}
}
