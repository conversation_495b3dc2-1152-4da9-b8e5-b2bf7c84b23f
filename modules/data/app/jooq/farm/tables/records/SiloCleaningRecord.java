/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.SiloCleaning;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SiloCleaningRecord extends UpdatableRecordImpl<SiloCleaningRecord> implements Record10<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Timestamp, String> {

	private static final long serialVersionUID = -1119977291;

	/**
	 * Setter for <code>farm.silo_cleaning.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.silo_id</code>.
	 */
	public void setSiloId(Long value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.silo_id</code>.
	 */
	public Long getSiloId() {
		return (Long) getValue(7);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.actor_date</code>.
	 */
	public void setActorDate(Timestamp value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.actor_date</code>.
	 */
	public Timestamp getActorDate() {
		return (Timestamp) getValue(8);
	}

	/**
	 * Setter for <code>farm.silo_cleaning.comment</code>.
	 */
	public void setComment(String value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.silo_cleaning.comment</code>.
	 */
	public String getComment() {
		return (String) getValue(9);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record10 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Timestamp, String> fieldsRow() {
		return (Row10) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Timestamp, String> valuesRow() {
		return (Row10) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return SiloCleaning.SILO_CLEANING.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return SiloCleaning.SILO_CLEANING.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return SiloCleaning.SILO_CLEANING.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return SiloCleaning.SILO_CLEANING.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return SiloCleaning.SILO_CLEANING.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return SiloCleaning.SILO_CLEANING.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return SiloCleaning.SILO_CLEANING.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field8() {
		return SiloCleaning.SILO_CLEANING.SILO_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field9() {
		return SiloCleaning.SILO_CLEANING.ACTOR_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field10() {
		return SiloCleaning.SILO_CLEANING.COMMENT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value8() {
		return getSiloId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value9() {
		return getActorDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value10() {
		return getComment();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value8(Long value) {
		setSiloId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value9(Timestamp value) {
		setActorDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord value10(String value) {
		setComment(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public SiloCleaningRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, Long value8, Timestamp value9, String value10) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached SiloCleaningRecord
	 */
	public SiloCleaningRecord() {
		super(SiloCleaning.SILO_CLEANING);
	}

	/**
	 * Create a detached, initialised SiloCleaningRecord
	 */
	public SiloCleaningRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, Long siloId, Timestamp actorDate, String comment) {
		super(SiloCleaning.SILO_CLEANING);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, siloId);
		setValue(8, actorDate);
		setValue(9, comment);
	}
}
