/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import com.cloudfarms.integrator.integration.TaskResult;

import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.FeedSystemScheduledTalkResponse;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FeedSystemScheduledTalkResponseRecord extends UpdatableRecordImpl<FeedSystemScheduledTalkResponseRecord> implements Record5<Long, Long, Timestamp, TaskResult, Timestamp> {

	private static final long serialVersionUID = -637427104;

	/**
	 * Setter for <code>farm.feed_system_scheduled_talk_response.id</code>.
	 */
	public void setId(Long value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.feed_system_scheduled_talk_response.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(0);
	}

	/**
	 * Setter for <code>farm.feed_system_scheduled_talk_response.feed_system_scheduling_id</code>.
	 */
	public void setFeedSystemSchedulingId(Long value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.feed_system_scheduled_talk_response.feed_system_scheduling_id</code>.
	 */
	public Long getFeedSystemSchedulingId() {
		return (Long) getValue(1);
	}

	/**
	 * Setter for <code>farm.feed_system_scheduled_talk_response.scheduled_response_timestamp</code>.
	 */
	public void setScheduledResponseTimestamp(Timestamp value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.feed_system_scheduled_talk_response.scheduled_response_timestamp</code>.
	 */
	public Timestamp getScheduledResponseTimestamp() {
		return (Timestamp) getValue(2);
	}

	/**
	 * Setter for <code>farm.feed_system_scheduled_talk_response.scheduled_response_data</code>.
	 */
	public void setScheduledResponseData(TaskResult value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.feed_system_scheduled_talk_response.scheduled_response_data</code>.
	 */
	public TaskResult getScheduledResponseData() {
		return (TaskResult) getValue(3);
	}

	/**
	 * Setter for <code>farm.feed_system_scheduled_talk_response.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.feed_system_scheduled_talk_response.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(4);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record5 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<Long, Long, Timestamp, TaskResult, Timestamp> fieldsRow() {
		return (Row5) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row5<Long, Long, Timestamp, TaskResult, Timestamp> valuesRow() {
		return (Row5) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field1() {
		return FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field2() {
		return FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE.FEED_SYSTEM_SCHEDULING_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field3() {
		return FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE.SCHEDULED_RESPONSE_TIMESTAMP;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<TaskResult> field4() {
		return FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE.SCHEDULED_RESPONSE_DATA;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field5() {
		return FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value2() {
		return getFeedSystemSchedulingId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value3() {
		return getScheduledResponseTimestamp();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TaskResult value4() {
		return getScheduledResponseData();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value5() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord value1(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord value2(Long value) {
		setFeedSystemSchedulingId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord value3(Timestamp value) {
		setScheduledResponseTimestamp(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord value4(TaskResult value) {
		setScheduledResponseData(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord value5(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FeedSystemScheduledTalkResponseRecord values(Long value1, Long value2, Timestamp value3, TaskResult value4, Timestamp value5) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached FeedSystemScheduledTalkResponseRecord
	 */
	public FeedSystemScheduledTalkResponseRecord() {
		super(FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE);
	}

	/**
	 * Create a detached, initialised FeedSystemScheduledTalkResponseRecord
	 */
	public FeedSystemScheduledTalkResponseRecord(Long id, Long feedSystemSchedulingId, Timestamp scheduledResponseTimestamp, TaskResult scheduledResponseData, Timestamp createDate) {
		super(FeedSystemScheduledTalkResponse.FEED_SYSTEM_SCHEDULED_TALK_RESPONSE);

		setValue(0, id);
		setValue(1, feedSystemSchedulingId);
		setValue(2, scheduledResponseTimestamp);
		setValue(3, scheduledResponseData);
		setValue(4, createDate);
	}
}
