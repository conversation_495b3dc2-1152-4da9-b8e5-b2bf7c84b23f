/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import com.fasterxml.jackson.databind.JsonNode;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.FarmViewRecord;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;

import utils.CfJsonNodeBinding;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FarmView extends TableImpl<FarmViewRecord> {

	private static final long serialVersionUID = 1393498093;

	/**
	 * The reference instance of <code>farm.farm_view</code>
	 */
	public static final FarmView FARM_VIEW = new FarmView();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<FarmViewRecord> getRecordType() {
		return FarmViewRecord.class;
	}

	/**
	 * The column <code>farm.farm_view.id</code>.
	 */
	public final TableField<FarmViewRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.name</code>.
	 */
	public final TableField<FarmViewRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "");

	/**
	 * The column <code>farm.farm_view.state</code>.
	 */
	public final TableField<FarmViewRecord, String> STATE = createField("state", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "");

	/**
	 * The column <code>farm.farm_view.details</code>.
	 */
	public final TableField<FarmViewRecord, Short> DETAILS = createField("details", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * The column <code>farm.farm_view.settings</code>.
	 */
	public final TableField<FarmViewRecord, JsonNode> SETTINGS = createField("settings", org.jooq.impl.DefaultDataType.getDefaultDataType("jsonb"), this, "", new CfJsonNodeBinding());

	/**
	 * The column <code>farm.farm_view.grids</code>.
	 */
	public final TableField<FarmViewRecord, JsonNode> GRIDS = createField("grids", org.jooq.impl.DefaultDataType.getDefaultDataType("jsonb"), this, "", new CfJsonNodeBinding());

	/**
	 * The column <code>farm.farm_view.create_date</code>.
	 */
	public final TableField<FarmViewRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.update_date</code>.
	 */
	public final TableField<FarmViewRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.create_account_id</code>.
	 */
	public final TableField<FarmViewRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.update_account_id</code>.
	 */
	public final TableField<FarmViewRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.create_ui</code>.
	 */
	public final TableField<FarmViewRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.farm_view.update_ui</code>.
	 */
	public final TableField<FarmViewRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>farm.farm_view</code> table reference
	 */
	public FarmView() {
		this("farm_view", null);
	}

	/**
	 * Create an aliased <code>farm.farm_view</code> table reference
	 */
	public FarmView(String alias) {
		this(alias, FARM_VIEW);
	}

	private FarmView(String alias, Table<FarmViewRecord> aliased) {
		this(alias, aliased, null);
	}

	private FarmView(String alias, Table<FarmViewRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<FarmViewRecord, Long> getIdentity() {
		return Keys.IDENTITY_FARM_VIEW;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<FarmViewRecord> getPrimaryKey() {
		return Keys.FARM_VIEW_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<FarmViewRecord>> getKeys() {
		return Arrays.<UniqueKey<FarmViewRecord>>asList(Keys.FARM_VIEW_PKEY, Keys.FARM_VIEW_NAME_STATE_UK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<FarmViewRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public FarmView as(String alias) {
		return new FarmView(alias, this);
	}

	/**
	 * Rename this table
	 */
	public FarmView rename(String name) {
		return new FarmView(name, null);
	}
}
