/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.tables.records.MedicineAllRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MedicineAll extends TableImpl<MedicineAllRecord> {

	private static final long serialVersionUID = -944039116;

	/**
	 * The reference instance of <code>farm.medicine_all</code>
	 */
	public static final MedicineAll MEDICINE_ALL = new MedicineAll();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<MedicineAllRecord> getRecordType() {
		return MedicineAllRecord.class;
	}

	/**
	 * The column <code>farm.medicine_all.id</code>.
	 */
	public final TableField<MedicineAllRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.medicine_all.medicine_id</code>.
	 */
	public final TableField<MedicineAllRecord, Long> MEDICINE_ID = createField("medicine_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.medicine_all.country_id</code>.
	 */
	public final TableField<MedicineAllRecord, String> COUNTRY_ID = createField("country_id", org.jooq.impl.SQLDataType.CHAR.length(2), this, "");

	/**
	 * The column <code>farm.medicine_all.name</code>.
	 */
	public final TableField<MedicineAllRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "");

	/**
	 * The column <code>farm.medicine_all.externalid</code>.
	 */
	public final TableField<MedicineAllRecord, String> EXTERNALID = createField("externalid", org.jooq.impl.SQLDataType.CLOB, this, "");

	/**
	 * The column <code>farm.medicine_all.category</code>.
	 */
	public final TableField<MedicineAllRecord, String> CATEGORY = createField("category", org.jooq.impl.SQLDataType.CLOB, this, "");

	/**
	 * The column <code>farm.medicine_all.company</code>.
	 */
	public final TableField<MedicineAllRecord, String> COMPANY = createField("company", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "");

	/**
	 * The column <code>farm.medicine_all.units</code>.
	 */
	public final TableField<MedicineAllRecord, BigDecimal> UNITS = createField("units", org.jooq.impl.SQLDataType.NUMERIC.precision(12, 2), this, "");

	/**
	 * The column <code>farm.medicine_all.unit</code>.
	 */
	public final TableField<MedicineAllRecord, String> UNIT = createField("unit", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.medicine_all.packages</code>.
	 */
	public final TableField<MedicineAllRecord, Short> PACKAGES = createField("packages", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * The column <code>farm.medicine_all.package</code>.
	 */
	public final TableField<MedicineAllRecord, String> PACKAGE = createField("package", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>farm.medicine_all.expiration_date</code>.
	 */
	public final TableField<MedicineAllRecord, Timestamp> EXPIRATION_DATE = createField("expiration_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.medicine_all.update_date</code>.
	 */
	public final TableField<MedicineAllRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.medicine_all.create_date</code>.
	 */
	public final TableField<MedicineAllRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.medicine_all.import_id</code>.
	 */
	public final TableField<MedicineAllRecord, String> IMPORT_ID = createField("import_id", org.jooq.impl.SQLDataType.VARCHAR, this, "");

	/**
	 * The column <code>farm.medicine_all.publicmedicine</code>.
	 */
	public final TableField<MedicineAllRecord, Boolean> PUBLICMEDICINE = createField("publicmedicine", org.jooq.impl.SQLDataType.BOOLEAN, this, "");

	/**
	 * The column <code>farm.medicine_all.add_sow</code>.
	 */
	public final TableField<MedicineAllRecord, BigDecimal> ADD_SOW = createField("add_sow", org.jooq.impl.SQLDataType.NUMERIC.precision(4, 1), this, "");

	/**
	 * The column <code>farm.medicine_all.add_weaner</code>.
	 */
	public final TableField<MedicineAllRecord, BigDecimal> ADD_WEANER = createField("add_weaner", org.jooq.impl.SQLDataType.NUMERIC.precision(6, 2), this, "");

	/**
	 * The column <code>farm.medicine_all.add_fattener</code>.
	 */
	public final TableField<MedicineAllRecord, BigDecimal> ADD_FATTENER = createField("add_fattener", org.jooq.impl.SQLDataType.NUMERIC.precision(6, 2), this, "");

	/**
	 * The column <code>farm.medicine_all.nplpackid</code>.
	 */
	public final TableField<MedicineAllRecord, String> NPLPACKID = createField("nplpackid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.medicine_all.nplid</code>.
	 */
	public final TableField<MedicineAllRecord, String> NPLID = createField("nplid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.medicine_all.medicine_category</code>.
	 */
	public final TableField<MedicineAllRecord, Short> MEDICINE_CATEGORY = createField("medicine_category", org.jooq.impl.SQLDataType.SMALLINT, this, "");

	/**
	 * Create a <code>farm.medicine_all</code> table reference
	 */
	public MedicineAll() {
		this("medicine_all", null);
	}

	/**
	 * Create an aliased <code>farm.medicine_all</code> table reference
	 */
	public MedicineAll(String alias) {
		this(alias, MEDICINE_ALL);
	}

	private MedicineAll(String alias, Table<MedicineAllRecord> aliased) {
		this(alias, aliased, null);
	}

	private MedicineAll(String alias, Table<MedicineAllRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedicineAll as(String alias) {
		return new MedicineAll(alias, this);
	}

	/**
	 * Rename this table
	 */
	public MedicineAll rename(String name) {
		return new MedicineAll(name, null);
	}
}
