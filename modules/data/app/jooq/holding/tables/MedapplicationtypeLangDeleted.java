/**
 * This class is generated by jOOQ
 */
package jooq.holding.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.holding.Holding;
import jooq.holding.Keys;
import jooq.holding.tables.records.MedapplicationtypeLangDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MedapplicationtypeLangDeleted extends TableImpl<MedapplicationtypeLangDeletedRecord> {

	private static final long serialVersionUID = -1043748707;

	/**
	 * The reference instance of <code>holding.medapplicationtype_lang_deleted</code>
	 */
	public static final MedapplicationtypeLangDeleted MEDAPPLICATIONTYPE_LANG_DELETED = new MedapplicationtypeLangDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<MedapplicationtypeLangDeletedRecord> getRecordType() {
		return MedapplicationtypeLangDeletedRecord.class;
	}

	/**
	 * The column <code>holding.medapplicationtype_lang_deleted.code</code>.
	 */
	public final TableField<MedapplicationtypeLangDeletedRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>holding.medapplicationtype_lang_deleted.language_id</code>.
	 */
	public final TableField<MedapplicationtypeLangDeletedRecord, String> LANGUAGE_ID = createField("language_id", org.jooq.impl.SQLDataType.VARCHAR.length(5).nullable(false), this, "");

	/**
	 * The column <code>holding.medapplicationtype_lang_deleted.update_date</code>.
	 */
	public final TableField<MedapplicationtypeLangDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false), this, "");

	/**
	 * The column <code>holding.medapplicationtype_lang_deleted.update_account_id</code>.
	 */
	public final TableField<MedapplicationtypeLangDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>holding.medapplicationtype_lang_deleted.update_ui</code>.
	 */
	public final TableField<MedapplicationtypeLangDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>holding.medapplicationtype_lang_deleted</code> table reference
	 */
	public MedapplicationtypeLangDeleted() {
		this("medapplicationtype_lang_deleted", null);
	}

	/**
	 * Create an aliased <code>holding.medapplicationtype_lang_deleted</code> table reference
	 */
	public MedapplicationtypeLangDeleted(String alias) {
		this(alias, MEDAPPLICATIONTYPE_LANG_DELETED);
	}

	private MedapplicationtypeLangDeleted(String alias, Table<MedapplicationtypeLangDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private MedapplicationtypeLangDeleted(String alias, Table<MedapplicationtypeLangDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Holding.HOLDING, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<MedapplicationtypeLangDeletedRecord> getPrimaryKey() {
		return Keys.MEDAPPLICATIONTYPE_LANG_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<MedapplicationtypeLangDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<MedapplicationtypeLangDeletedRecord>>asList(Keys.MEDAPPLICATIONTYPE_LANG_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<MedapplicationtypeLangDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeLangDeleted as(String alias) {
		return new MedapplicationtypeLangDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public MedapplicationtypeLangDeleted rename(String name) {
		return new MedapplicationtypeLangDeleted(name, null);
	}
}
