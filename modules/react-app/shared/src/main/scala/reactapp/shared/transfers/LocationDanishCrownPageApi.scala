package reactapp.shared.transfers

import sjsgrid.shared.grid.dto.{ DataGetterParams, FilteringAndSorting, GridFilters, RowModel, RowSaveDTO, ValidRow }

trait LocationDanishCrownPageApi {
  def list(params: DataGetterParams[LocationDanishCrownGrid]): Seq[(Long, RowModel[LocationDanishCrownGrid])]
  def count(filtering: GridFilters[LocationDanishCrownGrid]): Int
  def update(rowsDTO: Seq[RowSaveDTO[LocationDanishCrownGrid, Option[Long]]]): Map[Int, Long]
  def delete(id: Long): Unit
}
