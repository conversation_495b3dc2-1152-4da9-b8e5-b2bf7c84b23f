package reactapp.shared.budgeting

/**
 * Created by <PERSON> Date: 2.8.2017 Time: 9:48
 */

import domainz.location.InhabitantTypeEnum
import domainz.time.LocalDateInterval
import reactapp.shared.budgeting.BudgetingAndForecastingApi.ForecastEvent._
import reactapp.shared.budgeting.BudgetingAndForecastingApi.StrategyDestinationItem.InternalOrg
import reactapp.shared.budgeting.BudgetingAndForecastingApi._
import reactapp.shared.budgeting.transfer.strategy.TransferStrategyUnfolded.{ PigsPlan, WeekPlan }
import reactapp.shared.budgeting.transfer.strategy.{ TransferPlanAD, TransferPlanGrid, TransferStrategyUnfolded }
import reactapp.shared.feed.FeedTypeGrid
import reactapp.shared.feed.FeedTypeGrid.inhabitantTypeToRatioFieldMap
import reactapp.shared.grid.Field
import reactapp.shared.users.UsersPageApi.FarmId
import sjsgrid.shared.common.ModelType
import sjsgrid.shared.grid.dto.{ RowModel, RowSaveData }
import slogging.StrictLogging

import java.time.LocalDate
import scala.annotation.tailrec
import scala.math.BigDecimal.RoundingMode

object BudgetingAndForecastingCalculator extends StrictLogging {

  val forwardAlgorithm: Boolean = true

  private def updateTransfersToFarms(
    budgetRowId: BudgetRowId,
    pigsToDistribute: BigDecimal,
    pigsPlan: PigsPlan,
    userEnteredExpectedData: Map[BudgetRowId, UserEnteredExpectedData],
    whatToUseFn: UserEnteredExpectedData => Map[Long, BigDecimal],
  ): Map[StrategyDestinationItem, BigDecimal] = {

    val userEnteredTransfers =
      userEnteredExpectedData.get(budgetRowId).map(whatToUseFn).getOrElse(Map.empty)

    val userEnteredTransfersWithoutPrecalculated: Map[StrategyDestinationItem, BigDecimal] = {
      userEnteredTransfers.map {
        case (farmId, amount) =>
          InternalOrg(farmId).asInstanceOf[StrategyDestinationItem] -> amount
      } --
        pigsPlan.maxMove.flatMap(_.destinationOpt) --
        pigsPlan.remainOnFarm.destinationOpt --
        pigsPlan.restToFarm.farmUsedInPlan
    }

    def positiveOrZero(x: BigDecimal): BigDecimal = if (x >= BigDecimal(0)) x else BigDecimal(0)

    // First is processed remain: available pigs to distribute minus remain that should stay on farm minus
    // user entered transfers but without automatically override values
    val pigsToDistributeWithoutRemain = positiveOrZero(
      pigsToDistribute - pigsPlan.remainOnFarm.yearWeekMap.get(budgetRowId).map(_.amount).getOrElse(BigDecimal(0)) -
        userEnteredTransfersWithoutPrecalculated.foldLeft(BigDecimal(0))(_ + _._2),
    )

    val weekPlans = pigsPlan.maxMove.flatMap(transferPlan => {
      transferPlan.yearWeekMap
        .get(budgetRowId)
        .map(strategy => WeekPlan(transferPlan.destinationOpt.get, strategy.amount, strategy.priority))
    }).sortBy(_.priority)

    // next update all destinations by priority - it is defined by order in sequence remainPlan
    @tailrec
    def loop(
      accMap: Map[StrategyDestinationItem, BigDecimal],
      remainPlans: Seq[WeekPlan],
      currentPigsToDistribute: BigDecimal,
    ): (Map[StrategyDestinationItem, BigDecimal], BigDecimal) = {

      def calculateMoveAndRemain(planToMove: BigDecimal, remain: BigDecimal): (BigDecimal, BigDecimal) = {
        if (planToMove <= remain) {
          (planToMove, remain - planToMove)
        } else {
          (remain, BigDecimal(0))
        }
      }

      remainPlans.headOption match {
        case None => accMap -> currentPigsToDistribute
        case Some(weekPlan) =>
          val (pigsToMove, remain) = calculateMoveAndRemain(weekPlan.amount, currentPigsToDistribute)
          val updatedMap = {
            if (pigsToMove == 0)
              accMap - weekPlan.destination
            else
              accMap + (weekPlan.destination -> pigsToMove)
          }
          loop(updatedMap, remainPlans.tail, remain)
      }
    }

    val (updatedMapOfMoves, restOfPigs) =
      loop(userEnteredTransfersWithoutPrecalculated, weekPlans, pigsToDistributeWithoutRemain)

    // finally update the farm for rest of pigs
    pigsPlan.restToFarm.yearWeekMap.get(budgetRowId) match {
      case None => updatedMapOfMoves
      case Some(restPlan) => restPlan.destinationOpt match {
          case None => updatedMapOfMoves
          case Some(destination) =>
            if (updatedMapOfMoves.contains(destination)) {
              if (
                pigsPlan.maxMove.exists(transferPlan => {
                  transferPlan.destinationOpt.contains(destination) &&
                  transferPlan.yearWeekMap.get(budgetRowId).nonEmpty
                })
              ) {
                updatedMapOfMoves
              } else {
                updatedMapOfMoves + (destination -> restOfPigs)
              }
            } else {
              // if rest farm is not in the moves, it will be updated by rest
              updatedMapOfMoves + (destination -> restOfPigs)
            }
        }
    }

  }

  /**
   * Calculate budgeting and forecasting for the future
   *
   * @param factData
   *   Fact data for weeks only in past
   * @param sowsWithPigletsFarm
   *   Farm with sows and piglets
   * @param weanersFarm
   *   Farm with weaners
   * @param finishersFarm
   *   Farm with finishers
   * @param kpisMapFn
   *   Some KPIs evaluated from fact data from last periods
   * @param finisherSplitBoundary
   *   Boundary to split finishers to two weight group
   * @param userEnteredExpectedData
   *   Data entered by user for moves, transfers or sellings
   * @param calculationPeriod
   *   Interval from KPIs and averages are calculated for forecast
   * @param forecastInterval
   *   Interval for the forecast start and end
   * @param transferStrategies
   *   Strategies to auto fill of transfers
   * @param feedTypes
   *   Feed types by names used in forecast
   * @param firstDayOfWeek
   *   From farm settings
   * @param firstDayOfYear
   *   From farm settings
   * @return
   *   map of calculated forecast per weeks
   */

  def calculateForecast(
    factData: Map[BudgetRowId, BudgetingAndForecastingData],
    sowsWithPigletsFarm: Boolean,
    weanersFarm: Boolean,
    finishersFarm: Boolean,
    kpisMapFn: KpisForForecastFn,
    finisherSplitBoundary: Double,
    userEnteredExpectedData: Map[BudgetRowId, UserEnteredExpectedData],
    calculationPeriod: LocalDateInterval,
    forecastInterval: LocalDateInterval,
    transferStrategies: Seq[RowSaveData[TransferPlanGrid, TransferPlanAD]], // TODO: refactor RowSaveData to case class
    feedTypes: Seq[RowModel[FeedTypeGrid]],
    firstDayOfWeek: Int,
    firstDayOfYear: Int,
    bestPracticeFinishersPct: BigDecimal = 0,
  ): Map[BudgetRowId, BudgetingAndForecastingData] = {

    val time = System.currentTimeMillis()

    implicit val firstDays: FirstDaysSettings = FirstDaysSettings(firstDayOfWeek, firstDayOfYear)

    val factDataFromCalcPeriod = {
      BudgetingAndForecastingApi.factDataFromCalcPeriod(
        factData,
        calculationPeriod.from,
        calculationPeriod.to,
        firstDayOfWeek,
        firstDayOfYear,
      )
    }

    logger.debug(s""""First day of week" -> $firstDayOfWeek,""")
    logger.debug(s""""First day of year" -> $firstDayOfYear,""")
    logger.debug(s""""Fact data from calculation period" -> size = ${factDataFromCalcPeriod.size}, keys = ${factDataFromCalcPeriod.keys},""")
    logger.debug(s""""Sows with piglets farm" -> $sowsWithPigletsFarm,""")
    logger.debug(s""""Weaners farm" -> $weanersFarm,""")
    logger.debug(s""""Finishers farm" -> $finishersFarm,""")
    logger.debug(s"Current feed types: -> $feedTypes")

    def safeAvg(
      data: Map[BudgetRowId, BudgetingAndForecastingData],
      selectorFn: ((BudgetRowId, BudgetingAndForecastingData)) => BigDecimal,
    ): BigDecimal = {
      if (data.isEmpty)
        BigDecimal(0)
      else data.map(selectorFn).sum / data.size
    }

    def lactationWeeks(seasonalKpis: RowModel[MonthlyKpisGrid]): Int = {
      (seasonalKpis.getValue(MonthlyKpisGrid.UserRegularLactationPeriod).getOrElse(BigDecimal(4.0 * 7.0)) / 7)
        .setScale(0, RoundingMode.HALF_UP)
        .intValue
    }

    def weanerDays(seasonalKpis: RowModel[MonthlyKpisGrid]): BigDecimal = {
      (for {
        weanerExitAvgWeight <- seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerExitAvgWeight) if weanersFarm
        weanerEntryAvgWeight <- seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerEntryAvgWeight)
        weanerDailyGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerDailyWeightGain) if weanerDailyGain > 0
      } yield ((weanerExitAvgWeight - weanerEntryAvgWeight) / weanerDailyGain * 1000).abs).getOrElse(BigDecimal(7))
    }

    def finisherDays(seasonalKpis: RowModel[MonthlyKpisGrid]): BigDecimal = {
      (for {
        finisherEntryAvgWeight <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherEntryAvgWeight) if finishersFarm
        finisherExitAvgWeight <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherExitAvgWeight)
        finisherDailyGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain) if finisherDailyGain > 0
      } yield {
        ((finisherExitAvgWeight - finisherEntryAvgWeight) / finisherDailyGain * 1000)
          .abs.setScale(0, RoundingMode.HALF_UP)
      }).getOrElse(BigDecimal(11L * 7L)) // 11*7 - default constant when kpis are missing
    }

    def daysToWeeks(days: BigDecimal): Int = (days / 7.0).setScale(0, RoundingMode.HALF_UP).intValue

    val weanerWeeksFrom: RowModel[MonthlyKpisGrid] => Int = weanerDays _ andThen daysToWeeks
    val finisherWeeksFrom: RowModel[MonthlyKpisGrid] => Int = finisherDays _ andThen daysToWeeks

    val serveSowsAvg = {
      if (sowsWithPigletsFarm)
        safeAvg(factDataFromCalcPeriod, _._2.serveSows)
      else
        BigDecimal(0)
    }
    val serveGiltsAvg = {
      if (sowsWithPigletsFarm)
        safeAvg(factDataFromCalcPeriod, _._2.serveGilts)
      else
        BigDecimal(0)
    }
    val reservedSowsAvg = {
      if (sowsWithPigletsFarm)
        safeAvg(factDataFromCalcPeriod, _._2.reservedSows)
      else
        BigDecimal(0)
    }
    val finishersToYFAvg = safeAvg(factDataFromCalcPeriod, _._2.finishersToYF)
    val onStockYFAvg = safeAvg(factDataFromCalcPeriod, _._2.onStockYF)
    val onStockBoarsAvg = safeAvg(factDataFromCalcPeriod, _._2.onStockBoars)
    val onStockSowsAvg = safeAvg(factDataFromCalcPeriod, _._2.onStockSows)
    val weanersToYFAvg = safeAvg(factDataFromCalcPeriod, _._2.weanersToYF)
    logger.debug(s"weanersToYFAvg = $weanersToYFAvg")

    // val finisherBelowBoundaryWeeks = Math.round(finisherBelowBoundaryDays / 7.0).toInt

    val unfoldedTransferPlan = {
      TransferStrategyUnfolded.TransferPlanAllBuilder(
        transferStrategies,
        forecastInterval.to,
        firstDayOfWeek,
        firstDayOfYear,
      ).build()
    }
    logger.debug(unfoldedTransferPlan.toString)

    val sellExternalStrategyIsUsed = {
      transferStrategies.exists { row =>
        row.inputs(TransferPlanGrid.Destination).toOptionWhereNotAvailableIsNone
          .contains(StrategyDestinationItem.ExternalSell)
      }
    }
    logger.debug(s"sellExternalStrategyIsUsed = $sellExternalStrategyIsUsed")

    def feedUsableFor(
      feedTypeRowModel: RowModel[FeedTypeGrid],
      inhabitantType: InhabitantTypeEnum,
      weightOpt: Option[BigDecimal],
      date: LocalDate,
    ): Boolean = {

      def feedTypeIsFor(col: FeedTypeGrid with ModelType[Boolean] with Field.AlwaysFetch): Boolean =
        feedTypeRowModel.getValue(col).contains(true)

      import FeedTypeGrid._
      lazy val typeCorrect = inhabitantType match {
        case InhabitantTypeEnum.Weaners        => feedTypeIsFor(Weaner)
        case InhabitantTypeEnum.Fatteners      => feedTypeIsFor(Fattener)
        case InhabitantTypeEnum.Sow            => feedTypeIsFor(Sow)
        case InhabitantTypeEnum.Boar           => feedTypeIsFor(Boar)
        case InhabitantTypeEnum.Gilt           => feedTypeIsFor(Gilt)
        case InhabitantTypeEnum.Piglet         => feedTypeIsFor(Piglet)
        case InhabitantTypeEnum.SowWithParity0 => feedTypeIsFor(Sow)
      }

      lazy val reasonableWeight = weightOpt.forall { weight =>
        feedTypeRowModel.getValue(FeedTypeGrid.MinWeight).forall(_ <= weight) &&
        feedTypeRowModel.getValue(FeedTypeGrid.MaxWeight).forall(_ >= weight)
      }

      lazy val applicableDate = feedTypeRowModel.getValue(FeedTypeGrid.ValidFrom).forall(!date.isBefore(_)) &&
        feedTypeRowModel.getValue(FeedTypeGrid.ValidTo).forall(!date.isAfter(_))

      // logger.debug(
      //  s"Feed consideration: $date weight: $weightOpt " +
      //    s"type: $inhabitantType ${feedTypeRowModel.getValue(FeedTypeGrid.FeedName)} " +
      //    s"type correct: $typeCorrect weight OK: $reasonableWeight date OK: $applicableDate"
      // )
      // for gilt weight is not considered because is not calculated (known) in the forecast calculator
      typeCorrect && (inhabitantType == InhabitantTypeEnum.Gilt || reasonableWeight) && applicableDate
    }

    def getRatioForInhType(feed: RowModel[FeedTypeGrid], inhabitantType: InhabitantTypeEnum) =
      inhabitantTypeToRatioFieldMap.get(inhabitantType).flatMap(feed.getValue).getOrElse(BigDecimal(0))

    def calculateFeedRatio(feed: RowModel[FeedTypeGrid], inhabitantType: InhabitantTypeEnum, ratioTotal: BigDecimal): BigDecimal = {
      if (ratioTotal == BigDecimal(0)) BigDecimal(0)
      else getRatioForInhType(feed, inhabitantType) / ratioTotal
    }

    def ratioTotal(mayBeFeeds: Seq[RowModel[FeedTypeGrid]], inhabitantType: InhabitantTypeEnum): BigDecimal = {
      mayBeFeeds.foldLeft(BigDecimal(0)) {
        case (acc, feed) =>
          acc + getRatioForInhType(feed, inhabitantType)
      }
    }

    def generateFeedEvents(
      inhabitantType: InhabitantTypeEnum,
      growingNumber: BigDecimal,
      dead: BigDecimal,
      entryWeightOpt: Option[BigDecimal],
      dailyWeightGainOpt: Option[BigDecimal],
      conversionRatioOpt: Option[BigDecimal],
      fromDate: LocalDate,
      sourceRowId: BudgetRowId, // rowId where consumption start
      days: BigDecimal,
    ): Seq[(BudgetRowId, FeedConsumptionEvent)] = {
      (for {
        entryWeight <- entryWeightOpt
        dailyWeightGain <- dailyWeightGainOpt
        conversionRatio <- conversionRatioOpt
      } yield {
        // lastDayIndex: every started day counted as whole day (round up) and zero based
        val lastDayIndex = days.setScale(0, BigDecimal.RoundingMode.UP).intValue - 1
        (0 to lastDayIndex).flatMap { dayIndex =>
          val currentWeight = entryWeight + dailyWeightGain * dayIndex / 1000d
          val mayBeFeeds = feedTypes.filter(feedUsableFor(_, inhabitantType, Some(currentWeight), fromDate.plusDays(dayIndex)))
          // logger.debug(s"may be feeds for day $dayIndex: $mayBeFeeds")
          val feedRatioTotal = ratioTotal(mayBeFeeds, inhabitantType)
          val currentRowId = sourceRowId + (dayIndex / 7)
          mayBeFeeds.flatMap { feed =>
            feed.getValue(FeedTypeGrid.FeedName).map { feedName =>
              logger.debug(
                s"days: $days currentRowId: $currentRowId inhabitantType: $inhabitantType currentWeight: $currentWeight feedName: $feedName feedRatioTotal: $feedRatioTotal feedRatio: ${calculateFeedRatio(feed, inhabitantType, feedRatioTotal)}",
              )
              currentRowId -> FeedConsumptionEvent(
                feedName,
                calculateFeedRatio(
                  feed,
                  inhabitantType,
                  feedRatioTotal,
                ) * (growingNumber * dailyWeightGain * conversionRatio - dead * dailyWeightGain * conversionRatio / 2.0) / 1000,
                sourceRowId,
              )
            }
          }
        }
      }).getOrElse(Seq.empty)
    }

    def generateSowFeedEvents(
      inhabitantType: InhabitantTypeEnum,
      onStockAmount: BigDecimal,
      sowFeedWeightPerYearOpt: Option[BigDecimal],
      forDate: LocalDate,
      sourceRowId: BudgetRowId,
    ): Seq[(BudgetRowId, FeedConsumptionEvent)] = {
      for {
        sowFeedWeightPerYear <- sowFeedWeightPerYearOpt.toSeq
        dayIndex <- 0 to 6 // evaluated for all days in week because feedType using can stop or start in the middle of week
        mayBeFeeds = feedTypes.filter(feedUsableFor(_, inhabitantType, None, forDate.plusDays(dayIndex)))
        feedRatioTotal = ratioTotal(mayBeFeeds, inhabitantType)
        feed <- mayBeFeeds
        feedName <- feed.getValue(FeedTypeGrid.FeedName)
      } yield {
        logger.debug(
          s"for date: $forDate inhabitantType: $inhabitantType feedName: $feedName feedRatioTotal: $feedRatioTotal feedRatio: ${calculateFeedRatio(feed, inhabitantType, feedRatioTotal)}",
        )
        sourceRowId -> FeedConsumptionEvent(
          feedName,
          calculateFeedRatio(feed, inhabitantType, feedRatioTotal) * (onStockAmount * sowFeedWeightPerYear / 365),
          sourceRowId,
        )
      }

    }

    def calculateForecastRow(
      forDate: LocalDate,
      budgetRowId: BudgetRowId,
      accMap: Map[BudgetRowId, BudgetingAndForecastingData],
      lastWeanersToYFAmount: BigDecimal,
      events: Events,
    ): (Events, BudgetingAndForecastingData) = {

      logger.debug("/" * 80)
      logger.debug(s""""Calculate for" -> ${budgetRowId.toString},""")

      val seasonalKpis = kpisMapFn(forDate)
      logger.debug(s""""KPIs" -> $seasonalKpis,""")
      val weanerWeeks = weanerWeeksFrom(seasonalKpis)
      val finisherWeeks = finisherWeeksFrom(seasonalKpis)

      logger.debug(s"lactationWeeks = ${lactationWeeks(seasonalKpis)}")
      logger.debug(s"weanerWeeks = $weanerWeeks")
      logger.debug(s"finisherDays = ${finisherDays(seasonalKpis)}")
      logger.debug(s"finisherWeeks = ${finisherWeeks}")
      logger.debug(s"events.eventsMap.get(budgetRowId) = ${events.eventsMap.get(budgetRowId)}")

      val finisherBelowBoundaryDays = (for {
        finisherEntryAvgWeight <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherEntryAvgWeight) if finishersFarm
        finisherDailyGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain) if finisherDailyGain > 0
      } yield {
        if (finisherSplitBoundary > finisherEntryAvgWeight) {
          ((finisherSplitBoundary - finisherEntryAvgWeight) / finisherDailyGain * 1000)
            .abs.setScale(0, RoundingMode.HALF_UP)
        } else {
          BigDecimal(0)
        }
      }).getOrElse((finisherDays(seasonalKpis) * 0.4).setScale(0, RoundingMode.HALF_UP)) // default ratio is 40:60 (below:above finishers)
      logger.debug(s"finisherBelowBoundaryDays = $finisherBelowBoundaryDays")

      val oneWeekBackOpt = accMap.get(budgetRowId - 1)

      // After Lars explanation - serve Sows, Gilts and reserved sows from 12 weeks avg.
      val calculatedServeSows = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) serveSowsAvg else row.serveSows
        case None      => serveSowsAvg
      }
      logger.debug(s"calculatedServeSows = $calculatedServeSows")

      val serveSows = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) => if (value.additionalData.serveSowsUserChanged) value.serveSows else calculatedServeSows
        case None        => calculatedServeSows
      }
      logger.debug(s"serveSows = $serveSows")

      val calculatedServeGilts = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) serveGiltsAvg else row.serveGilts
        case None      => serveGiltsAvg
      }
      logger.debug(s"calculatedServeGilts = $calculatedServeGilts")

      val serveGilts = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) => if (value.additionalData.serveGiltsUserChanged) value.serveGilts else calculatedServeGilts
        case None        => calculatedServeGilts
      }
      logger.debug(s"serveGilts = $serveGilts")

      val calculatedReservedSows = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) reservedSowsAvg else row.reservedSows
        case None      => reservedSowsAvg
      }
      logger.debug(s"calculatedReservedSows = $calculatedReservedSows")

      val reservedSows = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) =>
          if (value.additionalData.reservedSowsUserChanged) value.reservedSows
          else calculatedReservedSows
        case None => calculatedReservedSows
      }
      logger.debug(s"reservedSows = $reservedSows")

      val allServedSows = serveSows + serveGilts + reservedSows
      logger.debug(s"allServedSows = $allServedSows")

      // TODO: move to events too
      val sowsFarrowed = accMap.get(budgetRowId - 16) match {
        case Some(row) =>
          if (sowsWithPigletsFarm)
            row.allServedSows * seasonalKpis.getValue(MonthlyKpisGrid.UserFarrowingRate).getOrElse(0) / 100
          else
            BigDecimal(0)
        case None => BigDecimal(0)
      }
      logger.debug(s"sowsFarrowed = $sowsFarrowed")

      val pigletsBorn = {
        if (sowsWithPigletsFarm)
          sowsFarrowed * seasonalKpis.getValue(MonthlyKpisGrid.UserLivebornLitter).getOrElse(0)
        else
          BigDecimal(0)
      }
      logger.debug(s"pigletsBorn = $pigletsBorn")

      val deadInFarrowing = {
        if (sowsWithPigletsFarm)
          pigletsBorn * seasonalKpis.getValue(MonthlyKpisGrid.UserPreweaningMortality).getOrElse(0) / 100
        else
          BigDecimal(0)
      }
      logger.debug(s"deadInFarrowing = $deadInFarrowing")

      val weanedSows = {
        if (sowsWithPigletsFarm) {
          if (forwardAlgorithm) {
            events.sumFor(SowWeanedEventPF, budgetRowId)
          } else {
            accMap.get(budgetRowId - lactationWeeks(seasonalKpis)) match {
              case Some(row) => row.sowsFarrowed
              case None      => BigDecimal(0)
            }
          }
        } else {
          BigDecimal(0)
        }
      }

      logger.debug(s"weanedSows = $weanedSows")

      val weanedPiglets = weanedSows * seasonalKpis.getValue(MonthlyKpisGrid.UserLivebornLitter).getOrElse(0) *
        ((100 - seasonalKpis.getValue(MonthlyKpisGrid.UserPreweaningMortality).getOrElse(BigDecimal(0))) / 100)
      logger.debug(s"weanedPiglets = $weanedPiglets")

      // val weanedPigletsTo = updateTransferToFarmForRemain(
      //  budgetRowId,
      //  weanedPiglets,
      //  weanedPigletsRemainderFarmId,
      //  weanedPigletsNotTransferedRemain,
      //  userEnteredExpectedData,
      //  _.weanedPigletsTo
      // )

      val weanedPigletsTo: Map[FarmId, BigDecimal] = {
        updateTransfersToFarms(
          budgetRowId,
          pigsToDistribute = weanedPiglets,
          pigsPlan = unfoldedTransferPlan.forWeanedPiglets,
          userEnteredExpectedData = userEnteredExpectedData,
          whatToUseFn = _.weanedPigletsTo,
        )
          .collect { case (StrategyDestinationItem.InternalOrg(id), amount) => id -> amount }
      }

      logger.debug(s"weanedPigletsTo = $weanedPigletsTo")

      val weanedPigletsFrom = userEnteredExpectedData.get(budgetRowId) match {
        case Some(row) => row.weanedPigletsFrom
        case None      => Map.empty[Long, BigDecimal]
      }
      logger.debug(s"weanedPigletsFrom = $weanedPigletsFrom")

      val weanersExternPurchaseOpt = userEnteredExpectedData.get(budgetRowId) match {
        case Some(row) => if (row.weanersExternPurchase == 0) None else Some(row.weanersExternPurchase)
        case _         => None
      }
      logger.debug(s"weanersExternPurchaseOpt = $weanersExternPurchaseOpt")

      val pigletsToWeanerSection = {
        weanedPiglets - weanedPigletsTo.foldLeft(BigDecimal(0))(_ + _._2) +
          weanedPigletsFrom.foldLeft(BigDecimal(0))(_ + _._2)
      }
      logger.debug(s"pigletsToWeanerSection = $pigletsToWeanerSection")

      val weanersDead = {
        if (weanersFarm) {
          (pigletsToWeanerSection + weanersExternPurchaseOpt.getOrElse(0)) *
            seasonalKpis.getValue(MonthlyKpisGrid.UserDeadWeaners).getOrElse(0) / 100
        } else {
          BigDecimal(0)
        }
      }
      logger.debug(s"weanersDead = $weanersDead")

      // Not in diagram
      val weanersToMove = {
        if (forwardAlgorithm) {
          events.sumFor(WeanersToFinishersEventPF, budgetRowId) - events.sumFor(WeanersDeadEventPF, budgetRowId)
        } else {
          accMap.get(budgetRowId - weanerWeeks) match { // 8 week by Idavang Excel
            case Some(row) =>
              if (weanersFarm)
                row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) - row.weanersDead
              else
                BigDecimal(0)
            case None => BigDecimal(0)
          }
        }
      }
      logger.debug(s"weanersToMove = $weanersToMove")

//      val weanersTo = updateTransferToFarmForRemain(
      //    budgetRowId,
      //    weanersToMove,
      //    weanersRemainderFarmId,
      //    weanersNotTransferedRemain,
      //    userEnteredExpectedData,
      //    _.weanersTo
      //  )

      val newWeanersTransfers = {
        updateTransfersToFarms(
          budgetRowId,
          pigsToDistribute = weanersToMove,
          pigsPlan = unfoldedTransferPlan.forWeaners,
          userEnteredExpectedData = userEnteredExpectedData,
          whatToUseFn = _.weanersTo,
        )
      }
      val weanersTo: Map[FarmId, BigDecimal] =
        newWeanersTransfers.collect { case (StrategyDestinationItem.InternalOrg(id), amount) => id -> amount }

      val weanersSoldExternByStrategyOpt = {
        newWeanersTransfers
          .collectFirst { case (StrategyDestinationItem.ExternalSell, amount) => amount }
      }

      logger.debug(s"weanersTo = $weanersTo")

      val weanersFrom = userEnteredExpectedData.get(budgetRowId) match {
        case Some(row) => row.weanersFrom
        case None      => Map.empty[Long, BigDecimal]
      }
      logger.debug(s"weanersFrom = $weanersFrom")

      val weanersToSum = weanersTo.foldLeft(BigDecimal(0))(_ + _._2) // This is SUM
      logger.debug(s"weanersToSum = $weanersToSum")

      val weanersFromSum = weanersFrom.foldLeft(BigDecimal(0))(_ + _._2)
      logger.debug(s"weanersFromSum = $weanersFromSum")

      val availableWeanersForYF = {
        if (finishersFarm) {
          // for external selling of weaners is used user plan
          val planToSellWeaners = {
            userEnteredExpectedData.get(budgetRowId) match {
              case Some(row) => row.weanersSoldExtern
              case _         => BigDecimal(0)
            }
          }
          (if (weanersFarm) {
             if (forwardAlgorithm) {
               events.sumFor(WeanersToFinishersEventPF, budgetRowId) - events.sumFor(WeanersDeadEventPF, budgetRowId)
             } else {
               accMap.get(budgetRowId - weanerWeeks) match { // 8 week by Excel
                 case Some(row) =>
                   if (weanersFarm)
                     row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) - row.weanersDead
                   else
                     BigDecimal(0)
                 case None =>
                   BigDecimal(0)
               }
             }
           } else BigDecimal(0)) - planToSellWeaners - weanersToSum + weanersFromSum
        } else {
          // is not finisher farm, so weaners end here
          if (weanersFarm) {
            if (forwardAlgorithm) {
              events.sumFor(WeanersToFinishersEventPF, budgetRowId) - events.sumFor(WeanersDeadEventPF, budgetRowId) -
                weanersToSum + weanersFromSum
            } else {
              accMap.get(budgetRowId - weanerWeeks).map { row =>
                row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) -
                  row.weanersDead - weanersToSum + weanersFromSum
              }.getOrElse(BigDecimal(0))
            }
          } else BigDecimal(0) // is not weaners farm and not finishers farm
        }
      }
      logger.debug(s"lastWeanersToYFAmount = $lastWeanersToYFAmount")
      logger.debug(s"availableWeanersForYF = $availableWeanersForYF")

      val calculatedWeanersToYF = lastWeanersToYFAmount.min(
        if (availableWeanersForYF >= 0) availableWeanersForYF
        else BigDecimal(0),
      )
      logger.debug(s"calculatedWeanersToYF = $calculatedWeanersToYF")

      val weanersToYF = {
        if (weanersFarm) {
          userEnteredExpectedData.get(budgetRowId) match {
            case Some(value) =>
              if (value.additionalData.weanersToYfUserChanged) value.weanersToYF
              else calculatedWeanersToYF
            case None => calculatedWeanersToYF
          }
        } else BigDecimal(0)
      }
      logger.debug(s"weanersToYF = $weanersToYF")

      val nextWeanersToYFAmount = {
        userEnteredExpectedData.get(budgetRowId) match {
          case Some(value) =>
            if (value.additionalData.weanersToYfUserChanged) value.weanersToYF
            else lastWeanersToYFAmount
          case None => lastWeanersToYFAmount
        }
      }
      logger.debug(s"nextWeanersToYFAmount = $nextWeanersToYFAmount")

      val weanersSoldExternOpt = {
        if (finishersFarm) {
          // for external selling of weaners is used strategy if defined and then user plan
          if (sellExternalStrategyIsUsed) {
            weanersSoldExternByStrategyOpt
          } else {
            userEnteredExpectedData.get(budgetRowId) match {
              case Some(row) => if (row.weanersSoldExtern == 0) None else Some(row.weanersSoldExtern)
              case None      => None
            }
          }
        } else {
          // is not finisher farm, so weaners end here, and strategy is ignored
          if (weanersFarm) {
            if (forwardAlgorithm) {
              val sum = events.sumFor(WeanersToFinishersEventPF, budgetRowId) - events.sumFor(WeanersDeadEventPF, budgetRowId) -
                weanersToSum + weanersFromSum - weanersToYF
              if (sum == BigDecimal(0)) None else Some(sum)
            } else {
              accMap.get(budgetRowId - weanerWeeks).map { row =>
                val amount = row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) -
                  row.weanersDead - weanersToSum + weanersFromSum - weanersToYF
                amount
              }
            }
          } else None // it is not weaners farm and not finishers farm
        }
      }
      logger.debug(s"weanersSoldOpt = $weanersSoldExternOpt")

      val finishersExternPurchaseOpt = userEnteredExpectedData.get(budgetRowId) match {
        case Some(row) => if (row.finishersExternPurchase == 0) None else Some(row.finishersExternPurchase)
        case _         => None
      }
      logger.debug(s"finishersExternPurchaseOpt = $finishersExternPurchaseOpt")

      val weanersToFinisherWithoutDeads = {
        if (finishersFarm) {
          (if (weanersFarm) {
             if (forwardAlgorithm) {
               events.sumFor(WeanersToFinishersEventPF, budgetRowId)
             } else {
               accMap.get(budgetRowId - weanerWeeks) match { // 8 week by Excel
                 case Some(row) =>
                   if (weanersFarm)
                     row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) // - row.weanersDead
                   else
                     BigDecimal(0)
                 case None =>
                   BigDecimal(0)
               }
             }
           } else BigDecimal(0)) - weanersSoldExternOpt.getOrElse(0) - weanersToSum + weanersFromSum - weanersToYF
        } else {
          BigDecimal(0)
        }
      }
      logger.debug(s"weanersToFinisherWithoutDeads = $weanersToFinisherWithoutDeads")

      val weanersDeadToHandle = oneWeekBackOpt.map(_.weanersDeadAcc).getOrElse(BigDecimal(0)) + (
        if (finishersFarm) {
          if (weanersFarm) {
            if (forwardAlgorithm) {
              events.sumFor(WeanersDeadEventPF, budgetRowId)
            } else {
              accMap.get(budgetRowId - weanerWeeks) match {
                case Some(row) =>
                  if (weanersFarm)
                    row.weanersDead
                  else BigDecimal(0)
                case None => BigDecimal(0)
              }
            }
          } else BigDecimal(0)
        } else BigDecimal(0)
      )
      logger.debug(s"weanersDeadToHandle = $weanersDeadToHandle")

      val weanersDeadToSubtract = {
        if (weanersToFinisherWithoutDeads <= 0) BigDecimal(0)
        else if (weanersToFinisherWithoutDeads >= weanersDeadToHandle) weanersDeadToHandle
        else weanersToFinisherWithoutDeads
      }
      logger.debug(s"weanersDeadToSubtract = $weanersDeadToSubtract")

      val weanersDeadAcc = weanersDeadToHandle - weanersDeadToSubtract
      logger.debug(s"weanersDeadAcc = $weanersDeadAcc")

      val weanersToFinishers = weanersToFinisherWithoutDeads - weanersDeadToSubtract

//      val weanersToFinishers =
//        if (userKpis.finishersFarm)
//          (if (userKpis.weanersFarm)
//            accMap.get(budgetRowId - weanerWeeksFrom) match { // 8 week by Excel
//              case Some(row) =>
//                if (userKpis.weanersFarm)
//                  row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0) - row.weanersDead
//                else
//                  BigDecimal(0)
//              case None =>
//                BigDecimal(0)
//            }
//          else BigDecimal(0)) - weanersSoldExternOpt.getOrElse(0) - weanersToSum + weanersFromSum - weanersToYF
//        else
//          BigDecimal(0)
      logger.debug(s"weanersToFinishers = $weanersToFinishers")

      val calculatedfinishersToYF = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) finishersToYFAvg else row.finishersToYF
        case None      => finishersToYFAvg
      }
      logger.debug(s"calculatedfinishersToYF = $calculatedfinishersToYF")

      val finishersToYF = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) =>
          if (value.additionalData.finishersToYfUserChanged) value.finishersToYF
          else calculatedfinishersToYF
        case None => calculatedfinishersToYF
      }
      logger.debug(s"finishersToYF = $finishersToYF")

      val finishersDead = {
        if (finishersFarm) {
          (weanersToFinishers + finishersExternPurchaseOpt.getOrElse(0)) *
            seasonalKpis.getValue(MonthlyKpisGrid.UserDeadFinishers).getOrElse(0) / 100
        } else {
          BigDecimal(0)
        }
      }
      logger.debug(s"finishersDead = $finishersDead")

      val finishersSoldWithoutDeads = {
        if (finishersFarm) {
          if (finisherWeeks == 0) {
            weanersToFinishers + finishersExternPurchaseOpt.getOrElse(0) - finishersToYF
          } else {
            if (forwardAlgorithm) {
              events.sumFor(FinishersSoldEventPF, budgetRowId)
            } else {
              accMap.get(budgetRowId - finisherWeeks) match { // 14 weeks by Excel
                case Some(row) => row.weanersToFinishers + row.finishersExternPurchase.getOrElse(0) - row.finishersToYF
                case None      => BigDecimal(0)
              }
            }
          }
        } else {
          BigDecimal(0)
        }
      }

      logger.debug(s"finishersSoldWithoutDeads = $finishersSoldWithoutDeads")

      val finishersDeadToHandle = oneWeekBackOpt.map(_.finishersDeadAcc).getOrElse(BigDecimal(0)) + (
        if (finishersFarm) {
          if (finisherWeeks == 0) finishersDead
          else {
            if (forwardAlgorithm) {
              events.sumFor(FinishersDeadEventPF, budgetRowId)
            } else {
              accMap.get(budgetRowId - finisherWeeksFrom(seasonalKpis)).map(_.finishersDead).getOrElse(BigDecimal(0))
            }
          }
        } else {
          BigDecimal(0)
        }
      )

      val finishersDeadToSubtract = {
        if (finishersSoldWithoutDeads <= 0) BigDecimal(0)
        else if (finishersSoldWithoutDeads >= finishersDeadToHandle) finishersDeadToHandle
        else finishersSoldWithoutDeads
      }

      val finishersDeadAcc = finishersDeadToHandle - finishersDeadToSubtract

      val finishersSold = finishersSoldWithoutDeads - finishersDeadToSubtract

      val finishersSoldBestPractice = finishersSold * bestPracticeFinishersPct / 100
      val finishersSoldStandard = finishersSold * (100 - bestPracticeFinishersPct) / 100

//      val finishersSold =
//        if (userKpis.finishersFarm)
//          if (finisherWeeksFrom == 0) {
//            weanersToFinishers + finishersExternPurchaseOpt.getOrElse(0) - finishersDead - finishersToYF
//          }
//          else
//            accMap.get(budgetRowId - finisherWeeks) match { // 14 weeks by Excel
//              case Some(row) =>
      //              row.weanersToFinishers + row.finishersExternPurchase.getOrElse(0)
      //              - row.finishersDead - row.finishersToYF
//              case None => BigDecimal(0)
//            }
//        else
//          BigDecimal(0)

      logger.debug(s"finishersSold = $finishersSold")

//      val finisherAvgWeight = periodKpis.finisherExitAvgWeight.getOrElse(BigDecimal(0))
      val finisherAvgWeight = seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherExitAvgWeight).getOrElse(BigDecimal(0))
      logger.debug(s"finisherAvgWeight = $finisherAvgWeight")

      val finishersWeight = finishersSold * finisherAvgWeight
      logger.debug(s"finishersWeight = $finishersWeight")

      val onStockPiglets = {
        (oneWeekBackOpt match {
          case Some(row) => row.onStockPiglets
          case None      => BigDecimal(0)
        }) + pigletsBorn - deadInFarrowing - weanedPiglets
      }
      logger.debug(s"onStockPiglets = $onStockPiglets")

      val calculatedonStockBoars = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) onStockBoarsAvg else row.onStockBoars
        case None      => onStockBoarsAvg
      }
      logger.debug(s"calculatedonStockBoars = $calculatedonStockBoars")

      val onStockBoars = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) =>
          if (value.additionalData.onStockBoarsUserChanged) value.onStockBoars
          else calculatedonStockBoars
        case None => calculatedonStockBoars
      }
      logger.debug(s"onStockBoars = $onStockBoars")

      val calculatedonStockYF = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) onStockYFAvg else row.onStockYF
        case None      => onStockYFAvg
      }
      logger.debug(s"calculatedonStockYF = $calculatedonStockYF")

      val onStockYF = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) => if (value.additionalData.onStockYfUserChanged) value.onStockYF else calculatedonStockYF
        case None        => calculatedonStockYF
      }
      logger.debug(s"onStockYF = $onStockYF")

      val calculatedonStockSows = oneWeekBackOpt match {
        case Some(row) => if (row.additionalData.isFactData) onStockSowsAvg else row.onStockSows
        case None      => onStockSowsAvg
      }
      logger.debug(s"calculatedonStockSows = $calculatedonStockSows")

      val onStockSows = userEnteredExpectedData.get(budgetRowId) match {
        case Some(value) =>
          if (value.additionalData.onStockSowsUserChanged) value.onStockSows
          else calculatedonStockSows
        case None => calculatedonStockSows
      }
      logger.debug(s"onStockSows = $onStockSows")

      val onStockWeaners = {
        (oneWeekBackOpt match {
          case Some(row) => row.onStockWeaners
          case None      => BigDecimal(0)
        }) + pigletsToWeanerSection + weanersExternPurchaseOpt.getOrElse(0) - // - weanersSold - weanersToSum -
          (if (forwardAlgorithm) {
             events.sumFor(WeanersToFinishersEventPF, budgetRowId)
           } else {
             accMap.get(budgetRowId - weanerWeeks) match {
               case Some(row) => row.pigletsToWeanerSection + row.weanersExternPurchase.getOrElse(0)
               case None      => BigDecimal(0)
             }
           })
      }
      logger.debug(s"onStockWeaners = $onStockWeaners")

      val onStockFinishers = {
        (oneWeekBackOpt match {
          case Some(row) => row.onStockFinishers
          case None      => BigDecimal(0)
        }) + weanersToFinishers + finishersExternPurchaseOpt.getOrElse(0) -
          finishersDead - finishersSold - (if (finishersFarm) finishersToYF else 0)
      }
      logger.debug(s"onStockFinishers = $onStockFinishers")

      val onStockFinishersBestPractice = onStockFinishers * bestPracticeFinishersPct / 100
      val onStockFinishersStandard = onStockFinishers * (100 - bestPracticeFinishersPct) / 100

      val onStockFinishersBelowBoundary = {
        if (finisherDays(seasonalKpis) > 0) onStockFinishers * finisherBelowBoundaryDays / finisherDays(seasonalKpis)
        else BigDecimal(0)
      }
      logger.debug(s"onStockFinishersBelowBoundary = $onStockFinishersBelowBoundary")

      val onStockFinishersAboveBoundary = onStockFinishers - onStockFinishersBelowBoundary
      logger.debug(s"onStockFinishersAboveBoundary = $onStockFinishersAboveBoundary")

      val currentEvents = events.eventsMap.getOrElse(budgetRowId, List.empty)
      val additionData = userEnteredExpectedData.get(budgetRowId) match {
        case Some(expectedData) =>
          expectedData.additionalData.copy(budgetRowId = budgetRowId, isFactData = false, forecastEvents = currentEvents)
        case None => AdditionalData(budgetRowId, isFactData = false, forecastEvents = currentEvents)
      }
      logger.debug(s"additionData = $additionData")

      val capacityWeaners = userEnteredExpectedData.get(budgetRowId) match {
        case Some(expectedData) => expectedData.capacityWeaners
        case None =>
          oneWeekBackOpt match {
            case Some(row) => row.capacityWeaners
            case None      => None
          }
      }
      logger.debug(s"capacityWeaners = $capacityWeaners")

      val capacityFinishers = userEnteredExpectedData.get(budgetRowId) match {
        case Some(expectedData) => expectedData.capacityFinishers
        case None =>
          oneWeekBackOpt match {
            case Some(row) => row.capacityFinishers
            case None      => None
          }
      }
      logger.debug(s"capacityFinishers = $capacityFinishers")

      val feedConsumptionSows = {
        if (sowsWithPigletsFarm) {
          seasonalKpis.getValue(MonthlyKpisGrid.UserSowFeedWeightPerYear) match {
            case Some(sowFeedWeightPerYear) => Some(onStockSows * (sowFeedWeightPerYear / daysInYear) * 7)
            case _                          => None
          }
        } else {
          None
        }
      }

      logger.debug(s"feedConsumptionSows = $feedConsumptionSows")

      val feedConsumptionYF = {
        for {
          ratio <- seasonalKpis.getValue(MonthlyKpisGrid.UserGiltFeedConvRatio)
          dailyWeightGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserGiltDailyWeightGain)
        } yield ratio * (dailyWeightGain / 1000) * onStockYF * 7
      }
      logger.debug(s"feedConsumptionYF = $feedConsumptionYF")

      val feedConsumptionWeaners = {
        if (weanersFarm) {
          for {
            ratio <- seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerFeedConvRatio)
            dailyWeightGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerDailyWeightGain)
          } yield ratio * (dailyWeightGain / 1000) * onStockWeaners * 7
        } else {
          None
        }
      }
      logger.debug(s"feedConsumptionWeaners = $feedConsumptionWeaners")

      val feedConsumptionFinishersBelowBoundary = {
        for {
          ratio <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherFeedConvRatio)
          dailyWeightGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain)
        } yield ratio * (dailyWeightGain / 1000) * onStockFinishersBelowBoundary * 7
      }
      logger.debug(s"feedConsumptionFinishersBelowBoundary = $feedConsumptionFinishersBelowBoundary")

      val feedConsumptionFinishersAboveBoundary = {
        for {
          ratio <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherFeedConvRatio)
          dailyWeightGain <- seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain)
        } yield ratio * (dailyWeightGain / 1000) * onStockFinishersAboveBoundary * 7
      }
      logger.debug(s"feedConsumptionFinishersAboveBoundary = $feedConsumptionFinishersAboveBoundary")

      val updatedEvents: Events = events
        .addEvents(
          Seq(
            budgetRowId + lactationWeeks(seasonalKpis) -> SowWeanedEvent(sowsFarrowed, budgetRowId),
            budgetRowId + weanerWeeks -> WeanersToFinishersEvent(pigletsToWeanerSection, weanersExternPurchaseOpt.getOrElse(0), budgetRowId),
            budgetRowId + weanerWeeks -> WeanersDeadEvent(weanersDead, budgetRowId),
            budgetRowId + finisherWeeks -> FinishersSoldEvent(
              weanersToFinishers,
              finishersExternPurchaseOpt.getOrElse(0),
              finishersToYF,
              budgetRowId,
            ),
            budgetRowId + finisherWeeks -> FinishersDeadEvent(finishersDead, budgetRowId),
          ),
        )
        .addEvents(
          if (weanersFarm) {
            generateFeedEvents(
              InhabitantTypeEnum.Weaners,
              pigletsToWeanerSection + weanersExternPurchaseOpt.getOrElse(BigDecimal(0)),
              weanersDead,
              seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerEntryAvgWeight),
              seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerDailyWeightGain),
              seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerFeedConvRatio),
              forDate,
              budgetRowId,
              weanerDays(seasonalKpis),
            )
          } else Seq.empty,
        )
        .addEvents(
          if (finishersFarm) {
            generateFeedEvents(
              InhabitantTypeEnum.Fatteners,
              weanersToFinishers + finishersExternPurchaseOpt.getOrElse(BigDecimal(0)),
              finishersDeadToSubtract,
              seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherEntryAvgWeight),
              seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain),
              seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherFeedConvRatio),
              forDate,
              budgetRowId,
              finisherDays(seasonalKpis),
            )
          } else Seq.empty,
        )
        .addEvents(
          if (sowsWithPigletsFarm) {
            generateSowFeedEvents(
              InhabitantTypeEnum.Sow,
              onStockSows,
              seasonalKpis.getValue(MonthlyKpisGrid.UserSowFeedWeightPerYear),
              forDate,
              budgetRowId,
            ) ++
              generateFeedEvents(
                InhabitantTypeEnum.Gilt,
                onStockYF,
                dead = BigDecimal(0), // not considered
                entryWeightOpt = Some(BigDecimal(0)), // for gilt does not matter
                dailyWeightGainOpt = seasonalKpis.getValue(MonthlyKpisGrid.UserGiltDailyWeightGain),
                conversionRatioOpt = seasonalKpis.getValue(MonthlyKpisGrid.UserGiltFeedConvRatio),
                fromDate = forDate,
                budgetRowId,
                days = BigDecimal(7),
              )
          } else Seq.empty,
        )

      val usedFeeds = updatedEvents.eventsMap.get(budgetRowId).map(_.collect(FeedConsumptionEventNamesPF)).getOrElse(List.empty)
      val feedConsumptionMap = usedFeeds.map { feedName =>
        feedName -> updatedEvents.sumFor(FeedConsumptionEventPF(feedName), budgetRowId)
      }.toMap

      logger.debug(s"current event list: ${updatedEvents.eventsMap.get(budgetRowId)}")
      // logger.debug(s"current feed map: $feedConsumptionMap")

      updatedEvents -> BudgetingAndForecastingData(
        additionData,
        serveSows,
        serveGilts,
        reservedSows,
        allServedSows,
        sowsFarrowed,
        pigletsBorn,
        deadInFarrowing,
        weanedSows,
        weanedPiglets,
        weanedPigletsTo,
        weanedPigletsFrom,
        pigletsToWeanerSection,
        weanersDead,
        weanersDeadAcc,
        weanersExternPurchaseOpt,
        weanersSoldExternOpt,
        weanersTo,
        weanersFrom,
        weanersToYF,
        weanersToFinishers,
        finishersExternPurchaseOpt,
        finishersToYF,
        finishersDead,
        finishersDeadAcc,
        finishersSold,
        finishersSoldBestPractice,
        finishersSoldStandard,
        finishersWeight,
        finisherAvgWeight,
        onStockPiglets,
        onStockBoars,
        onStockYF,
        onStockSows,
        onStockWeaners,
        onStockFinishersBelowBoundary,
        onStockFinishersAboveBoundary,
        onStockFinishers,
        onStockFinishersBestPractice,
        onStockFinishersStandard,
        capacityWeaners,
        capacityFinishers,
        efficiencyWeaners = capacityWeaners.flatMap { capacity =>
          if (capacity == 0) None else Some(onStockWeaners / capacity * 100)
        },
        efficiencyFinishers = capacityFinishers.flatMap { capacity =>
          if (capacity == 0) None else Some(onStockFinishers / capacity * 100)
        },
        feedConsumptionSows,
        feedConsumptionYF,
        feedConsumptionWeaners,
        feedConsumptionFinishersBelowBoundary,
        feedConsumptionFinishersAboveBoundary,
        feedConsumption = feedConsumptionMap,
        nextWeanersToYFAmount,
      )
    }

    def calculateFactOrForecastRow(
      forDate: LocalDate,
      budgetRowId: BudgetRowId,
      accMap: Map[BudgetRowId, BudgetingAndForecastingData],
      lastWeanersToYFAmount: BigDecimal,
      events: Events,
    ): (Events, BudgetingAndForecastingData) = {
      factData.get(budgetRowId) match {
        case Some(factDataRow) => // fact data
          val seasonalKpis = kpisMapFn(forDate)
          val weanerWeeks = weanerWeeksFrom(seasonalKpis)
          val finisherWeeks = finisherWeeksFrom(seasonalKpis)
          val newEvents = events
            .addEvents(
              Seq(
                budgetRowId + lactationWeeks(seasonalKpis) -> SowWeanedEvent(factDataRow.sowsFarrowed, budgetRowId),
                budgetRowId + weanerWeeks -> WeanersToFinishersEvent(
                  factDataRow.pigletsToWeanerSection,
                  factDataRow.weanersExternPurchase.getOrElse(0),
                  budgetRowId,
                ),
                budgetRowId + weanerWeeks -> WeanersDeadEvent(factDataRow.weanersDead, budgetRowId),
                budgetRowId + finisherWeeks -> FinishersSoldEvent(
                  factDataRow.weanersToFinishers,
                  factDataRow.finishersExternPurchase.getOrElse(0),
                  factDataRow.finishersToYF,
                  budgetRowId,
                ),
                budgetRowId + finisherWeeks -> FinishersDeadEvent(factDataRow.finishersDead, budgetRowId),
              ),
            )
            .addEvents(
              if (weanersFarm) {
                generateFeedEvents(
                  InhabitantTypeEnum.Weaners,
                  factDataRow.pigletsToWeanerSection + factDataRow.weanersExternPurchase.getOrElse(BigDecimal(0)),
                  factDataRow.weanersDead,
                  seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerEntryAvgWeight),
                  seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerDailyWeightGain),
                  seasonalKpis.getValue(MonthlyKpisGrid.UserWeanerFeedConvRatio),
                  forDate,
                  budgetRowId,
                  weanerDays(seasonalKpis),
                )
              } else Seq.empty,
            )
            .addEvents(
              if (finishersFarm) {
                generateFeedEvents(
                  InhabitantTypeEnum.Fatteners,
                  factDataRow.weanersToFinishers + factDataRow.finishersExternPurchase.getOrElse(BigDecimal(0)),
                  factDataRow.finishersDead,
                  seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherEntryAvgWeight),
                  seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherDailyWeightGain),
                  seasonalKpis.getValue(MonthlyKpisGrid.UserFinisherFeedConvRatio),
                  forDate,
                  budgetRowId,
                  finisherDays(seasonalKpis),
                )
              } else Seq.empty,
            )
            .addEvents(
              if (sowsWithPigletsFarm) {
                generateSowFeedEvents(
                  InhabitantTypeEnum.Sow,
                  factDataRow.onStockSows,
                  seasonalKpis.getValue(MonthlyKpisGrid.UserSowFeedWeightPerYear),
                  forDate,
                  budgetRowId,
                ) ++
                  generateFeedEvents(
                    InhabitantTypeEnum.Gilt,
                    factDataRow.onStockYF,
                    BigDecimal(0),
                    Some(BigDecimal(0)),
                    seasonalKpis.getValue(MonthlyKpisGrid.UserGiltDailyWeightGain),
                    seasonalKpis.getValue(MonthlyKpisGrid.UserGiltFeedConvRatio),
                    forDate,
                    budgetRowId,
                    BigDecimal(7),
                  )
              } else Seq.empty,
            )

          newEvents -> factDataRow.copy(
            additionalData = factDataRow.additionalData.copy(forecastEvents = newEvents.eventsMap.getOrElse(budgetRowId, List.empty)),
            pigletsToWeanerSection =
              factDataRow.weanedPiglets -
                factDataRow.weanedPigletsTo.foldLeft(BigDecimal(0))(_ + _._2) +
                factDataRow.weanedPigletsFrom.foldLeft(BigDecimal(0))(_ + _._2),
            lastWeanersToYFAmount = lastWeanersToYFAmount,
          )
        // forecasted data
        case None => calculateForecastRow(forDate, budgetRowId, accMap, lastWeanersToYFAmount, events)
      }
    }

    @tailrec
    def simulationLoop(
      forDate: LocalDate,
      accMap: Map[BudgetRowId, BudgetingAndForecastingData],
      lastWeanersToYFAmount: BigDecimal,
      events: Events,
    ): Map[BudgetRowId, BudgetingAndForecastingData] = {

      if (forDate.isAfter(forecastInterval.to)) {
        accMap
      } else {
        val forDateWeekInfo = WeekInfo.weekInfo(forDate, firstDayOfWeek, firstDayOfYear)
        val calRowId = BudgetRowId(forDateWeekInfo.year, forDateWeekInfo.week)
        val (updatedEvents, rowToAdd) =
          calculateFactOrForecastRow(forDate, calRowId, accMap, lastWeanersToYFAmount, events)
        simulationLoop(forDate.plusWeeks(1), accMap + ((calRowId, rowToAdd)), rowToAdd.lastWeanersToYFAmount, updatedEvents)
      }
    }

    val startYear = (forecastInterval.to.getYear - 1).min(calculationPeriod.from.getYear)
    val ret = simulationLoop(
      forDate = WeekInfo.weekStart(startYear, 1, firstDayOfWeek, firstDayOfYear),
      accMap = Map.empty,
      lastWeanersToYFAmount = weanersToYFAvg,
      events = Events(),
    )
    logger.info("Calculation time [ms] -> " + (System.currentTimeMillis() - time))
    ret
  }

}
