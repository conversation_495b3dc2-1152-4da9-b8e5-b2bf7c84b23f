package graphql

/**
  * Created by Milan Satala
  * Date: 10. 12. 2020
  * Time: 10:05
  */
trait GraphQLOAuth {
  def clientId: String

  /**
    * Account must have all these app roles to access anything.
    */
  def requiredAppRoles: Set[String] = Set.empty
}

object GraphQLOAuth {
  val autoTokenForLoggedInUser = Map(
    "demo" -> new GraphQLOAuth {
      override def clientId: String = "demo"
    },
    "admin" -> new GraphQLOAuth {
      override def clientId: String = "cf-internal"

      override val requiredAppRoles: Set[String] = Set("admin")
    },
    "backoffice" -> new GraphQLOAuth {
      override def clientId: String = "cf-internal"

      override val requiredAppRoles: Set[String] = Set("backOffice")
    },
    "developer" -> new GraphQLOAuth {
      override def clientId: String = "cf-internal"

      override val requiredAppRoles: Set[String] = Set("developer")
    },
  )
}
