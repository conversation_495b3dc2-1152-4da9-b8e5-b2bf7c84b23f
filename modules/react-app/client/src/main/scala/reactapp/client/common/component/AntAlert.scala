package reactapp.client.common.component

import org.scalajs
import org.scalajs.dom
import sjsgrid.client.common.ScalaJsOps.o
import sjsgrid.flash.client.facade.AntJs
import slinky.core.FunctionalComponent
import slinky.core.annotations.react
import slinky.core.facade.ReactElement
import slinky.web.html_<^.<

import scala.collection.immutable.IndexedSeq
import scala.scalajs.js

@react object AntAlert {
  case class Props(
    alertType: AlertType,
    message: ReactElement,
    descriptionOpt: Option[ReactElement] = None,
    closable: Boolean = false,
    showIcon: Boolean = false,
    afterCloseFn: () => Unit = () => (),
  )

  val component: FunctionalComponent[Props] = FunctionalComponent[Props] { props =>
    val afterCloseCall = () => {
      props.afterCloseFn()
      // timer for race-condition
      // call resize to force Autosize component rerender
      js.timers.setTimeout(0) {
        dom.window.dispatchEvent(new scalajs.dom.Event("resize"))
      }
    }

    val oo = props.descriptionOpt match {
      case Some(description) =>
        o(
          message = props.message,
          `type` = props.alertType.alertType,
          description = description,
          closable = props.closable,
          showIcon = props.showIcon,
          afterClose = afterCloseCall,
        )
      case _ =>
        o(
          message = props.message,
          `type` = props.alertType.alertType,
          closable = props.closable,
          showIcon = props.showIcon,
          afterClose = afterCloseCall,
        )
    }

    AntJs.Alert(oo)
  }

  sealed trait AlertType extends enumeratum.EnumEntry {
    def alertType: String = entryName.toLowerCase
  }

  object AlertType extends enumeratum.Enum[AlertType] {
    override def values: IndexedSeq[AlertType] = findValues
    case object Success extends AlertType

    case object Info extends AlertType

    case object Warning extends AlertType

    case object Error extends AlertType
  }
}
