package reactapp.client.common.component

import japgolly.scalajs.react._
import japgolly.scalajs.react.vdom.html_<^._
import org.scalajs.dom.ext.KeyCode
import org.scalajs.jquery._
import reactapp.client.common.AngularAppUtils
import reactapp.client.common.CloudFarmsOps._
import sjsgrid.client.common.react.ExtVar
import sjsgrid.shared.common.dto.WeekYear

import scala.scalajs.js

/**
  * <PERSON> 1.8.2016
  */

case class WeekPicker(
  valueEV: ExtVar[String],
  ng: AngularAppUtils,
  onClose: Callback = Callback.empty,
  focusOnMount: Boolean = false,
  additionalAttributes: TagMod = EmptyVdom,
) {

  def apply(): VdomElement = WeekPicker.cmp(this)
}

object WeekPicker {
  type Props = WeekPicker

  type State = js.Date

  val highlightWeeks: js.Function1[JQueryEventObject, js.Any] =
    (e: JQueryEventObject) => jQuery(e.target).closest("tr").find("td a").addClass("ui-state-hover")

  class Backend($ : BackendScope[Props, State]) {
    lazy val $datepickerWidget: JQuery = jQuery("#ui-datepicker-div")
    lazy val $datepickerInput: JQuery = jQuery($.getDOMNode.runNow().asMounted().asElement().getElementsByClassName("weekpicker"))
    lazy val $datepickerButton: JQuery = jQuery($.getDOMNode.runNow().asMounted().asElement().getElementsByTagName("button"))

    val stopHighlightWeeks: js.Function1[JQueryEventObject, js.Any] =
      (e: JQueryEventObject) => jQuery(e.target).closest("tr").find("td a").removeClass("ui-state-hover")

    var umounting = false
    def componentDidMount() = Callback {

      val initialProps = $.props.runNow()

      val ourOptions = js.Dynamic.global.angular.extend(
        obj(
          "showOn" -> "button",
          "buttonText" -> "<i class='awesome-icon-calendar'></i>",
          "showOtherMonths" -> true,
          "selectOtherMonths" -> true,
          "changeMonth" -> true,
          "changeYear" -> true,
          "beforeShowDay" -> { (dateFromDP: js.Date) =>
            jQuery(".ui-state-active").removeClass("ui-state-active")
            val actualProps = $.props.runNow()
            val parsedDate = WeekYear.parseOpt(actualProps.valueEV.value) match {
              case Some(wy) =>
                actualProps.ng.jsConversion.weekYearToWeekStart(wy)
              case None =>
                initialProps.ng.jsConversion.weekYearToWeekStart(WeekYear.parseOpt(initialProps.valueEV.value).get)
            }
            if (initialProps.ng.jsConversion.jsDateToWeekNumber(parsedDate) == initialProps.ng.jsConversion.jsDateToWeekNumber(dateFromDP))
              obj("0" -> true, "1" -> "ui-state-active")
            else
              obj("0" -> true, "1" -> "")
          },
          "onSelect" -> { () =>
            val props = $.props.runNow()
            val rawInput = $datepickerInput.value().asInstanceOf[String]
            val parsedInputDate = props.ng.jsConversion.parseJsDate(rawInput)
            val parsedWeekYear = props.ng.jsConversion.jsDateToWeekYear(parsedInputDate)
            val formattedWeekYear = parsedWeekYear.toString

            props.valueEV.setCb(formattedWeekYear).runNow()
          },
          "onClose" -> { () =>
            $datepickerWidget.off("mousemove", "td a", highlightWeeks)
            $datepickerWidget.off("mouseleave", "td a", stopHighlightWeeks)
            if (!umounting) {
              $datepickerInput.focus()

              $.props.runNow().onClose.runNow()
            }
          },
        ),
        initialProps.ng.angularAppServices.uiConfig.date,
      )

      if (initialProps.focusOnMount)
        $datepickerInput.focus().select()
      val dp = $datepickerInput.dynamic.datepicker(ourOptions)
      // button is created by datepicker, so we need to init it before binding event handler to it
      $datepickerButton.on("click", () => $datepickerWidget.on("mousemove", "td a", highlightWeeks))
      $datepickerButton.on("click", () => $datepickerWidget.on("mouseleave", "td a", stopHighlightWeeks))
      dp
    }

    def componentWillUnmount() = Callback {
      umounting = true
      js.Dynamic.global.$.datepicker.dpDiv.stop(true, true)
      $datepickerInput.dynamic.datepicker("hide")
      $datepickerInput.dynamic.datepicker("destroy")
    }

    def render(props: Props, state: State): VdomElement = <.div(
      ^.cls := "alignedRight",
      ^.onKeyDown ==> ((e: ReactKeyboardEvent) => {
        Callback {
          if (
            e.keyCode == KeyCode.Space && (props.valueEV.value.isEmpty || {
              val input = $datepickerInput.get(0).dynamic
              input.selectionStart.asInt == 0 && input.selectionEnd.asInt == input.value.length.asInt
            })
          ) {
            $datepickerButton.on("click", () => $datepickerWidget.on("mousemove", "td a", highlightWeeks))
            $datepickerButton.on("click", () => $datepickerWidget.on("mouseleave", "td a", stopHighlightWeeks))
            $datepickerInput.dynamic.datepicker("show")
            e.preventDefault()
            e.stopPropagation()
          }
        }
      }),
      TextInput(props.valueEV, additionalAttributes = props.additionalAttributes)(),
      <.input(^.`type` := "hidden", ^.cls := "weekpicker", ^.value := props.ng.formatLocalDate(props.ng.jsConversion.jsDateToLocalDate(state))),
    )
  }

  val cmp = ScalaComponent.builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(props => props.ng.jsConversion.weekYearToWeekStart(WeekYear.parseOpt(props.valueEV.value).get))
    .renderBackend[Backend]
    .componentDidMount(params => params.backend.componentDidMount())
    .componentDidUpdate(params => {
      Callback {
        if (params.currentProps.valueEV.value != params.prevProps.valueEV.value) {
          WeekYear.parseOpt(params.currentProps.valueEV.value) match {
            case Some(wy) =>
              params.setState(params.currentProps.ng.jsConversion.weekYearToWeekStart(wy)).runNow()
            case None =>
              ()
          }
        }
      }
    })
    .componentWillUnmount(_.backend.componentWillUnmount())
    .build
}
