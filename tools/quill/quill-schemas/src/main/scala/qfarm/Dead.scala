package qfarm

case class Dead(
  createDate: java.time.Instant,
  updateDate: java.time.Instant,
  createAccountId: Long,
  updateAccountId: Long,
  id: Long,
  sowId: Option[Long],
  boarId: Option[Long],
  servingId: Option[Long],
  giltId: Option[Long],
  illnesstypeCode: Option[String],
  deathtypeCode: Option[String],
  comment: Option[String],
  actorId: Option[Long],
  actorDate: java.time.Instant,
  locationId: Option[Long],
  weight: Option[scala.math.BigDecimal],
  pigCentralrn: Option[String],
  amount: Option[Int],
  age: Option[Int],
  price: Option[scala.math.BigDecimal],
  pigletState: Option[Short],
)
