package qroot.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class ForecastOrganization(
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  organizationId: DynamicValue[Long] = DynamicValue.Unavailable,
  forecastId: DynamicValue[Long] = DynamicValue.Unavailable,
  sowsWithPigletsFarm: DynamicValue[Option[Boolean]] = DynamicValue.Unavailable,
  weanersFarm: DynamicValue[Option[Boolean]] = DynamicValue.Unavailable,
  finishersFarm: DynamicValue[Option[Boolean]] = DynamicValue.Unavailable,
  processingOrder: DynamicValue[Option[Int]] = DynamicValue.Unavailable,
)

object ForecastOrganization {
  import quill.QuillContext._

  implicit def setForecastOrganization(v: ForecastOrganization): Seq[DynamicSet[qroot.ForecastOrganization, _]] = {
    val set = ValueSetter[qroot.ForecastOrganization]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.id)(v.id),
      set(_.organizationId)(v.organizationId),
      set(_.forecastId)(v.forecastId),
      set(_.sowsWithPigletsFarm)(v.sowsWithPigletsFarm),
      set(_.weanersFarm)(v.weanersFarm),
      set(_.finishersFarm)(v.finishersFarm),
      set(_.processingOrder)(v.processingOrder),
    )
  }
}
