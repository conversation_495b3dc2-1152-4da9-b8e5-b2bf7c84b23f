package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class DefecttypeDisabled(
  code: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object DefecttypeDisabled {
  import quill.QuillContext._

  implicit def setDefecttypeDisabled(v: DefecttypeDisabled): Seq[DynamicSet[qfarm.DefecttypeDisabled, _]] = {
    val set = ValueSetter[qfarm.DefecttypeDisabled]
    Seq(
      set(_.code)(v.code),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
