package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class BreedeqLastchange(
  id: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
)

object BreedeqLastchange {
  import quill.QuillContext._

  implicit def setBreedeqLastchange(v: BreedeqLastchange): Seq[DynamicSet[qfarm.BreedeqLastchange, _]] = {
    val set = ValueSetter[qfarm.BreedeqLastchange]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
    )
  }
}
