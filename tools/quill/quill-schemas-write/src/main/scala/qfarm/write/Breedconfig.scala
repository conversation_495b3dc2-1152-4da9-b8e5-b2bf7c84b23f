package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class Breedconfig(
  breed: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  minteststartage: DynamicValue[Short] = DynamicValue.Unavailable,
  maxteststartage: DynamicValue[Short] = DynamicValue.Unavailable,
  maxtesteduntil: DynamicValue[Short] = DynamicValue.Unavailable,
  minstartweight: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
  maxstartweight: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
)

object Breedconfig {
  import quill.QuillContext._

  implicit def setBreedconfig(v: Breedconfig): Seq[DynamicSet[qfarm.Breedconfig, _]] = {
    val set = ValueSetter[qfarm.Breedconfig]
    Seq(
      set(_.breed)(v.breed),
      set(_.updateDate)(v.updateDate),
      set(_.minteststartage)(v.minteststartage),
      set(_.maxteststartage)(v.maxteststartage),
      set(_.maxtesteduntil)(v.maxtesteduntil),
      set(_.minstartweight)(v.minstartweight),
      set(_.maxstartweight)(v.maxstartweight),
    )
  }
}
