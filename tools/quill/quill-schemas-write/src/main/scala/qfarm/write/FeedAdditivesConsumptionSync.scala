package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class FeedAdditivesConsumptionSync(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  traceabilityId: DynamicValue[String] = DynamicValue.Unavailable,
  syncDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  syncStatus: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  parentId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
)

object FeedAdditivesConsumptionSync {
  import quill.QuillContext._

  implicit def setFeedAdditivesConsumptionSync(v: FeedAdditivesConsumptionSync): Seq[DynamicSet[qfarm.FeedAdditivesConsumptionSync, _]] = {
    val set = ValueSetter[qfarm.FeedAdditivesConsumptionSync]
    Seq(
      set(_.id)(v.id),
      set(_.traceabilityId)(v.traceabilityId),
      set(_.syncDate)(v.syncDate),
      set(_.syncStatus)(v.syncStatus),
      set(_.parentId)(v.parentId),
    )
  }
}
