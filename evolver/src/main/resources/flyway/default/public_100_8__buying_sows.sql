create or replace function common.serving_location_check_aiurtf() returns trigger as $$
declare
  buying_date timestamptz;
begin
  select ti.actor_date into buying_date
  from transferindividualin tii
  inner join transferin ti on tii.transferin_id = ti.id
  where tii.sow_id = new.sow_id;
  if (new.actor_date <= buying_date) then
    if new.location_id is not null then
      raise exception 'The serving location has to be null before the sow has been bought % <= %', new.actor_date, buying_date using errcode = 'check_violation';
    end if;
  else
    if new.location_id is null then
      raise exception 'The serving location must not be null' using errcode = 'check_violation';
    end if;
  end if;
  if (new.farrow_starttime <= buying_date) then
    if new.farrow_location_id is not null then
      raise exception 'The farrowing location has to be null before the sow has been bought  % < %', new.farrow_starttime, buying_date using errcode = 'check_violation';
    end if;
  else
    if new.farrow_location_id is null and new.farrow_starttime is not null then
      raise exception 'The farrowing location must not be null' using errcode = 'check_violation';
    end if;
  end if;
  return new;
end;
$$ language plpgsql;

create or replace function common.serving_x_location_check_aiurtf() returns trigger as $$
declare
  buying_date timestamptz;
begin
  select ti.actor_date into buying_date
  from transferindividualin tii
    inner join transferin ti on tii.transferin_id = ti.id
    inner join serving i on i.sow_id = tii.sow_id
  where i.id = new.serving_id;
  if (new.actor_date <= buying_date) then
    if new.location_id is not null then
      raise exception 'The location has to be null before the sow has been bought' using errcode = 'check_violation';
    end if;
  else
    if new.location_id is null then
      raise exception 'The location must not be null' using errcode = 'check_violation';
    end if;
  end if;
  return new;
end;
$$ language plpgsql;

create or replace function common.weaned_location_check_aiurtf() returns trigger as $$
declare
  buying_date timestamptz;
begin
  select ti.actor_date into buying_date
  from transferindividualin tii
    inner join transferin ti on tii.transferin_id = ti.id
    inner join serving i on i.sow_id = tii.sow_id
  where i.id = new.serving_id;
  if (new.actor_date <= buying_date) then
    if new.location_id is not null then
      raise exception 'The location has to be null before the sow has been bought' using errcode = 'check_violation';
    end if;
    if new.sow_location_id is not null then
      raise exception 'The sow location has to be null before the sow has been bought % % % ', new.sow_location_id , new.actor_date , buying_date using errcode = 'check_violation';
    end if;
  else
    if new.location_id is null then
      raise exception 'The location must not be null' using errcode = 'check_violation';
    end if;
    if new.sow_location_id is null then
      raise exception 'The sow location must not be null' using errcode = 'check_violation';
    end if;
  end if;
  return new;
end;
$$ language plpgsql;
