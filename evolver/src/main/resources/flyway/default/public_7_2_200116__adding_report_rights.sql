INSERT INTO organizationrole (id, name, mobile_role) VALUES (157, 'danishGeneticsWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (156, 'danishGeneticsRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (155, 'matingPlanWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (154, 'matingPlanRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (153, 'sowsstatesReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (152, 'kpiCustomizationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (151, 'transferReportOrganizationsRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (150, 'transferReportFarmsRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (149, 'transferReportLocationsRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (148, 'deadInsightReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (147, 'customReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (146, 'costsReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (145, 'reproductionCycleReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (144, 'failedPregnanciesRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (143, 'servingWeekDaysRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (142, 'lactationDaysReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (141, 'giltAgeRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (140, 'parityReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (139, 'centralRegistryRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (138, 'accountingRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (137, 'statisticsRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (136, 'dashboardReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (135, 'dayReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (134, 'parityBreedRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (133, 'efficiencyHoldingReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (132, 'feedconsumptionReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (131, 'efficiencyReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (130, 'boarSuccessReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (129, 'serverSuccessReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (128, 'servingListRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (127, 'fertilityReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (126, 'productionReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (125, 'farm2FarmReportRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (124, 'emptyLocationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (123, 'emptyLocationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (122, 'boxListWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (121, 'boxListRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (120, 'backFatBoarWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (119, 'backFatBoarRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (118, 'kpiGoalWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (117, 'kpiGoalRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (116, 'pigletTransferWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (115, 'pigletTransferRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (114, 'ticanLocationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (113, 'ticanLocationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (112, 'ticanIntegrationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (111, 'ticanIntegrationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (110, 'danishCrownLocationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (109, 'danishCrownLocationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (108, 'danishCrownIntegrationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (107, 'danishCrownIntegrationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (106, 'communicationHistoryWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (105, 'communicationHistoryRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (104, 'feedStationWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (103, 'feedStationRead', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (102, 'feedSystemWrite', null) on conflict do nothing;
INSERT INTO organizationrole (id, name, mobile_role) VALUES (101, 'feedSystemRead', null) on conflict do nothing;

insert into public.persona_role
select pr.persona_id, orrnew.id
from (values
             ('backFatBoarRead', 'backFatRead'),
             ('backFatBoarWrite', 'backFatWrite'),
             ('danishCrownIntegrationWrite', 'sellingWrite'),
             ('danishCrownLocationWrite', 'admin'),
             ('danishCrownLocationRead', 'sellingRead'), --check
             ('stocktakingRead', 'boxListRead'),
             ('stocktakingWrite', 'boxListWrite'),
             ('ticanIntegrationWrite', 'sellingWrite'),
             ('boxListRead', 'stocktakingRead'),
             ('boxListWrite', 'stocktakingWrite'),
             ('kpiGoalRead', 'admin'),
             ('kpiGoalWrite', 'admin'),
             ('matingPlanWrite', 'servingWrite'),
             ('emptyLocationRead', 'groupTransferRead'),
             ('emptyLocationWrite', 'groupTransferWrite')) as r(new_role, old_role)
         join public.organizationrole orr on r.old_role = orr.name
         join public.organizationrole orrnew on r.new_role = orrnew.name
         join public.persona_role pr on pr.role_id = orr.id
         join persona p on pr.persona_id = p.id
where not p.disabled;

insert into public.persona_role
select persona_id, new_id
from (select distinct persona_id from public.persona_role pr join persona p on pr.persona_id = p.id where not p.disabled) as a
    cross join (select id new_id from public.organizationrole where name in
                                                                    ('productionReportRead',
                                                                     'fertilityReportRead',
                                                                     'fertilityReportRead',
                                                                     'servingListRead',
                                                                     'serverSuccessReportRead',
                                                                     'boarSuccessReportRead',
                                                                     'efficiencyReportRead',
                                                                     'feedconsumptionReportRead',
                                                                     'parityReportRead',
                                                                     'statisticsRead',
                                                                     'accountingRead',
                                                                     'centralRegistryRead',
                                                                     'parityReportRead',
                                                                     'giltAgeRead',
                                                                     'lactationDaysReportRead',
                                                                     'servingWeekDaysRead',
                                                                     'failedPregnanciesRead',
                                                                     'reproductionCycleReportRead',
                                                                     'costsReportRead',
                                                                     'customReportRead',
                                                                     'deadInsightReportRead',
                                                                     'transferReportLocationsRead',
                                                                     'transferReportFarmsRead',
                                                                     'transferReportOrganizationsRead')) as b;
insert into public.persona_role
select a.id, b.new_id from
    (select p.id
     from public.cfmodule mod
              join organization_module om on om.cfmodule_id = mod.id
              join persona p on p.organization_id = om.organization_id
     where name = 'review' and not p.disabled) as a
        cross join (select id new_id from public.organizationrole where name in ('feedSystemRead', 'feedStationWrite', 'feedStationRead', 'feedStationWrite', 'communicationHistoryRead')) as b;

insert into public.persona_role
select
    distinct persona_id,
    (select id new_id from public.organizationrole where name like 'danishCrownIntegrationRead')
from public.persona_role pr
join persona p on pr.persona_id = p.id
where not p.disabled and role_id in
      (select id old_id from public.organizationrole where name like 'sellingRead' or name like 'admin');

insert into public.persona_role
select
    distinct persona_id,
    (select id new_id from public.organizationrole where name like 'ticanIntegrationRead')
from public.persona_role pr
         join persona p on pr.persona_id = p.id
where not p.disabled and role_id in
      (select id old_id from public.organizationrole where name like 'sellingRead' and name like 'admin');

insert into public.persona_role
select
    distinct persona_id,
    (select id new_id from public.organizationrole where name like 'pigletTransferWrite')
from public.persona_role pr
         join persona p on pr.persona_id = p.id
where not p.disabled and role_id in
      (select id old_id from public.organizationrole where name like 'admin' or name like 'farrowingWrite');

insert into public.persona_role
select
    distinct persona_id,
    (select id new_id from public.organizationrole where name like 'pigletTransferRead')
from public.persona_role pr
         join persona p on pr.persona_id = p.id
where not p.disabled and role_id in
      (select id old_id from public.organizationrole where name like 'farrowingRead' and name like 'tracksPigletsMovements');  --CHECK

insert into public.persona_role
select a.id, b.new_id from
    (select distinct p.id
     from public.cfmodule mod
              join organization_module om on om.cfmodule_id = mod.id
              join persona p on p.organization_id = om.organization_id
              join persona_role pr on p.id = pr.persona_id
     where not p.disabled and name = 'danavl' and pr.role_id in (select id old_id from public.organizationrole where name like 'admin')
    ) as a
        cross join (select id new_id from public.organizationrole where name in ('danishGeneticsRead', 'danishGeneticsWrite')) as b;

insert into public.persona_role
select a.id, b.new_id from
    (select distinct p.id
     from public.cfmodule mod
              join organization_module om on om.cfmodule_id = mod.id
              join persona p on p.organization_id = om.organization_id
              join persona_role pr on p.id = pr.persona_id
     where not p.disabled and name = 'matingPlan' and pr.role_id in (select id old_id from public.organizationrole where name like 'admin' or name like 'servingRead')
    ) as a
        cross join (select id new_id from public.organizationrole where name in ('matingPlanRead')) as b;
