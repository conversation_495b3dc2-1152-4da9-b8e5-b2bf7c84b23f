package cfpigbotlink

import domains.kpi.calculator.KpiCalculatorService
import domains.reports.nonpregnancy.FailedPregnanciesReportRepo
import reports.kpi.average.AverageKpisService
import reports.kpi.intermediate.IntermediateKpisService

trait LinkModule extends KpiModule with ApplicationUserModule {
  lazy val failedPregnanciesReportRepo = new FailedPregnanciesReportRepo()
  lazy val kpiGetter = new KpiGetter(sessionProvider, kpisDataSourceService, applicationUserHandler, kpiDefinitionService, setting, kpiCalculatorService)
  lazy val intermediateKpisService = new IntermediateKpisService(kpiDefinitionService, kpisDataSourceService, applicationUserHandler, settingsHandler)
  lazy val averageKpisService = new AverageKpisService(intermediateKpisService)
  lazy val linkActivitiesImpl: LinkActivitiesImpl =
    new LinkActivitiesImpl(kpiGetter, settingsHandler, failedPregnanciesReportRepo, sessionProvider, messagesApi, kpiDefinitionService, intermediateKpisService, averageKpisService, applicationUserHandler)
  lazy val temporalService = new TemporalService(configuration)
  lazy val kpiCalculatorService = new KpiCalculatorService(kpiDefinitionService, kpisDataSourceService, dynamicSpaceCache)
}
