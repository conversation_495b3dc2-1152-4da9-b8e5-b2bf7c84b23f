package utils;

import akka.stream.Materializer;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import play.db.jpa.JPAApi;
import play.i18n.MessagesApi;
import play.libs.Json;
import play.mvc.Call;
import play.mvc.Http;
import play.mvc.Result;
import play.test.Helpers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

/**
 * <AUTHOR> <<EMAIL>>
 *         This class provides simple utils for dropping, creating and filling in the test database "cloudfarmstest"
 *         Date: 9/13/13
 *         <p>
 *         This test class starts play application before class and than stops it after class. Also database is dropped and restored from initdb.dump file.
 *         For more info have a look at {@link utils.CleanDbTestBase#cleanDB()}
 */
public class CleanDbTest extends CleanDbTestBase {

    protected static final String SECURITY_HASH = "cb106e18de2d859c45906815c3e6da303d5de8ef";

    @BeforeClass
    public static void startApp() {
        CleanDbTestBase.cleanDB();
        Map<String, String> testConfiguration = new HashMap();
        testConfiguration.put("db.default.url", CleanDbTestBase.getDBUrl());
        testConfiguration.put("db.auth.url", CleanDbTestBase.getDBUrl());
        testConfiguration.put("db.reports.url", CleanDbTestBase.getDBUrl());
        testConfiguration.put("application.langs", "en,da,de,hu,lt,lv,pl,ro,ru,sk,uk");
        testConfiguration.put("chores.suspended", "true");
        testConfiguration.put("db.default.hikaricp.maximumPoolSize", "3");
        testConfiguration.put("db.reports.hikaricp.maximumPoolSize", "3");
        testConfiguration.put("db.auth.hikaricp.maximumPoolSize", "3");
        testConfiguration.put("db.default.hikaricp.minIdle", "0");
        testConfiguration.put("db.reports.hikaricp.minIdle", "0");
        testConfiguration.put("db.auth.hikaricp.minIdle", "0");
        CleanDbTestBase.app = Helpers.fakeApplication(testConfiguration);
        Helpers.start(CleanDbTestBase.app);
        CleanDbTestBase.jpaApi = CleanDbTestBase.app.injector().instanceOf(JPAApi.class);
        CleanDbTestBase.messagesApi = CleanDbTestBase.app.injector().instanceOf(MessagesApi.class);
    }

    @AfterClass
    public static void stopApp() {
        Helpers.stop(CleanDbTestBase.app);
    }

    public static void assertOKStatus(Result result) {
        assertEquals(Optional.of("application/json"), result.contentType());
        JsonNode nodeOut = Json.parse(Helpers.contentAsString(result));
        if (Http.Status.OK != result.status()) {
            fail("Expected OK, but got: " + result.status() + "\n" + " with: " + nodeOut.findValue("description") + "\n");
        }
    }

    public static void assertForbiddenStatus(Result result) {
        assertForbiddenStatus(result, null);
    }

    public static void assertForbiddenStatus(Result result, Long id) {
        if (id == null) {
            assertEquals(Optional.of("application/json"), result.contentType());
            JsonNode nodeOut = Json.parse(Helpers.contentAsString(result));
            if (Http.Status.FORBIDDEN != result.status()) {
                fail("Expected FORBIDDEN, but got: " + result.status() + "\n" + " with: " + nodeOut.findValue("description") + "\n");
            }
        } else {
            assertEquals(Optional.of("application/json"), result.contentType());
            JsonNode nodeOut = Json.parse(Helpers.contentAsString(result));

            if (Http.Status.FORBIDDEN != nodeOut.get(id + "").get("status").asInt()) {
                List<JsonNode> messages = nodeOut.findValues("description");
                String messagesConcat = " with:\n";
                for (JsonNode m : messages) {
                    messagesConcat += m.textValue() + "\n";
                }
                fail("Expected FORBIDDEN, but got: " + nodeOut.get(id + "").get("status").asInt() + messagesConcat + "\n");
            }

        }
    }

    public static void assertUnAuthorizedStatus(Result result) {
        assertEquals(Optional.of("application/json"), result.contentType());
        JsonNode nodeOut = Json.parse(Helpers.contentAsString(result));
        if (Http.Status.UNAUTHORIZED != result.status()) {
            fail("Expected UNAUTHORIZED, but got: " + result.status() + "\n" + " with: " + nodeOut.findValue("description") + "\n");
        }
    }

    public static void assertOKStatus(Result result, Long id) {
        assertEquals(Optional.of("application/json"), result.contentType());
        JsonNode nodeOut = Json.parse(Helpers.contentAsString(result));
        assertTrue("Should contain status of the id " + id, nodeOut.hasNonNull(Long.toString(id, 10)));
        if (Http.Status.OK != nodeOut.get(Long.toString(id, 10)).get("status").asInt()) {
            List<JsonNode> messages = nodeOut.findValues("description");
            String messagesConcat = " with:\n";
            for (JsonNode m : messages) {
                messagesConcat += m.textValue() + "\n";
            }
            fail("Expected OK, but got: " + nodeOut.get(Long.toString(id, 10)).get("status").asInt() + messagesConcat + "\n");
        }
    }

    public static void printJsonNode(JsonNode n) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        System.out.println("\n" + mapper.writeValueAsString(n) + "\n");
    }

    protected Http.RequestBuilder fakeRequestWithSessionData(Call call) {
        return Helpers.fakeRequest(call).session("farm", "3").session("t", Long.toString(new java.util.Date().getTime())).session("account", "1").session("hash", SECURITY_HASH);
    }

    protected Result callActionWithWebSession(Call call) {
        return Helpers.route(CleanDbTestBase.app,fakeRequestWithSessionData(call));
    }

    protected String processMobileSync(String action) {
        return processMobileSync(action, false);
    }

    protected String processMobileSync(String action, boolean waitForBody) {
        Materializer mat = CleanDbTestBase.app.injector().instanceOf(Materializer.class);
        Http.RequestBuilder post = Helpers.fakeRequest("POST", "/sync").bodyText(action);
        Result result = Helpers.route(CleanDbTestBase.app,post, 300_000L);
        String resultContent = Helpers.contentAsString(result, mat);
        return resultContent;
    }
}
