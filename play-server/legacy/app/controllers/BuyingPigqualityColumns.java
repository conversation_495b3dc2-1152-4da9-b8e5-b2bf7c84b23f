package controllers;

import models.PigqualityIn;
import models.handlers.SettingsHandler;
import play.i18n.Messages;
import security.ApplicationUser;

import jakarta.persistence.EntityManager;

import static controllers.GridColumn.Align.left;
import static controllers.GridColumn.Align.right;
import static controllers.GridColumn.ColumnType.number;
import static controllers.GridColumn.ColumnType.text;
import static controllers.GridColumn.Editor.*;
import static controllers.GridColumn.Filter.filterLocation;
import static controllers.GridColumn.Filter.filterWeightKg;
import static controllers.GridColumn.Formatter.*;
import static controllers.GridColumn.Validator.aboveZero;
import static controllers.GridColumn.Validator.mandatory;

public class BuyingPigqualityColumns extends ScreenColumns<PigqualityIn> {

    // Note: This is copy-paste PigqualityColumns.java with commented-out pigWeight, deadAmount, deadWeight.
    public BuyingPigqualityColumns(final Messages messages, final ApplicationUser user, EntityManager em, SettingsHandler settingsHandler) {
        addCol(col("locationId").name("js.label.location").editor(null).formatter(formatLocation(em)).width(12).align(right).filter(filterLocation).filterValue("null").validator(mandatory));
        addCol(col("inhabitanttype_code").editor(null).width(10).name("js.label.inhabitanttype").cannotTriggerInsert(true));
        addCol(col("pigQualityTypeCode").editor(codesAutoComplete("PigqualityTypes")).width(9).formatter(formatCodeTypes(em, "PigqualityTypes")).cannotTriggerInsert(true));
        addCol(col("pigCentralRn").type(text).editor(maxLength(30)).width(11).sortable(false).cannotTriggerInsert(true));
        addCol(col("pigAmount").type(number).editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).width(10).cannotTriggerInsert(true));
        if (settingsHandler.useAgeInGroups(user)) {
            addCol(col("age").type(number).editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).width(10).cannotTriggerInsert(true).validator(aboveZero));
        }
        addCol(col("pigLiveWeight").localizedName(messages.apply("js.label.pigliveweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
            .type(number).editor(weightKg(3)).formatter(formatWeightKg(3)).filter(filterWeightKg).width(13).cannotTriggerInsert(true));
        addCol(col("pigAvgWeight").localizedName(messages.apply("js.label.avgweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
            .editor(weightKg(3)).type(number).formatter(formatWeightKg(3)).filter(filterWeightKg).width(13).cannotTriggerInsert(true));
        addCol(col("price").name("js.label.transferprice").type(number).editor(decimalPlaces(3)).formatter(formatDecimalPlaces(3)).width(15).cannotTriggerInsert(true));
        addCol(col("leanPercent").type(number).editor(decimalPlaces(2, 5)).formatter(formatDecimalPlaces(2)).width(7).cannotTriggerInsert(true));
        addCol(col("comment").type(text).editor(maxLength(250)).width(40).align(left).cannotTriggerInsert(true));
    }

}
