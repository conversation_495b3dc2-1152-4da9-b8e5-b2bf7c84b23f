package controllers;

import models.handlers.MedicineHandler;
import play.api.http.FileMimeTypes;
import play.data.FormFactory;
import play.mvc.*;
import play.mvc.Result;
import security.*;
import utils.Utils;
import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.*;

/**
 * Created by <PERSON><PERSON> on 6.5.2014.
 */
@Singleton
public class PrescriptionController extends RestController {


    private final MedicineHandler medicineHandler;

    @Inject
    public PrescriptionController(play.Application application, MedicineHandler medicineHandler, FileMimeTypes fileMimeTypes, FormFactory formFactory) {
        super(application, fileMimeTypes);

        this.medicineHandler = medicineHandler;
    }


    @WithTenant({@Group(farm = FarmRole.prescriptionRead), @Group(farm = FarmRole.prescriptionRead)})
    public Result listValidAt(Long dateTime, Http.Request request) {
        if (dateTime <= 0) return internalServerError();
        return withEm(request, em -> ok(Utils.toJson(medicineHandler.getPrescriptionsValidAt(em, new Date(dateTime)))));
        // TODO: what about time zone?
    }
}
