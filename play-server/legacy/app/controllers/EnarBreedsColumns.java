package controllers;
import reactapp.shared.enar.EnarLOV;
import java.util.*;
import static controllers.GridColumn.ColumnType.number;
import static controllers.GridColumn.ColumnType.text;
import static controllers.GridColumn.Editor.integerSelectEditor;
import static controllers.GridColumn.Editor.maxLength;
import static controllers.GridColumn.Formatter.intMapperFormatter;
public class EnarBreedsColumns extends ScreenColumns<Object> {
    public EnarBreedsColumns() {
        addCol(col("breed").name("js.label.breed").type(text).editor(maxLength(30)).width(20));
        addCol(col("enar_code").name("js.label.enar_code").type(number)
            .editor(integerSelectEditor(new LinkedHashMap(EnarLOV.javaBreed()), false))
            .formatter(intMapperFormatter(EnarLOV.javaBreed()))
            .width(50));
    }
}
