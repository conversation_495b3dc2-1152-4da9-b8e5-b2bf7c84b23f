package controllers.feed

import akka.actor.{ ActorSystem, Props }
import com.cloudfarms.integrator.integration.CanLog

import javax.inject.Inject
import controllers.ws.feed.FeedSystemContext
import controllers.ws.feed.nedap._
import jooq.public_.Tables.ORGANIZATION
import play.api.Configuration
import play.api.i18n.I18nSupport
import play.api.libs.json.Json
import play.api.libs.ws._
import play.api.mvc._
import reactapp.shared.auth.{ Access, Authorization }
import tenant.{ SessionProvider, WebActionBuilder }
import upickle.default._
import com.cloudfarms.integrator.integration.utils.DateFormatters.LongWriter
import domains.common.M
import domains.common.grid2.ServerContextBuilder
import utils.{ DBProvider, HtmlUtils }
import utils.api.NedapWS

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }
import scala.util.{ Failure, Success }

/**
  * Created by <PERSON> on 30.1.2016.
  */
class NedapController @Inject() (
  tenantActionBuilder: WebActionBuilder,
  configuration: Configuration,
  actorSystem: ActorSystem,
  db: DBProvider,
  nedapDbUtils: NedapDbUtils,
  nedapWS: NedapWS,
  nedapFeedSystemActorFactory: NedapFeedSystemActor.Factory,
  cc: ControllerComponents,
)(implicit ec: ExecutionContext, sessionProvider: SessionProvider, serverContextBuilder: ServerContextBuilder)
    extends AbstractController(cc) with CanLog with I18nSupport {
  def settings = Action {
    val nedapUrl = configuration.get[String]("nedap.authorize.url")
    val nedapClientId = configuration.get[String]("nedap.client.id")
    Ok(Json.obj("authorizeUrl" -> nedapUrl, "clientId" -> nedapClientId))
  }

  /**
   * This method is used to delete the nedap cache. It can be used to delete all the nedap cache or cache for specific sow names.
   * @param names - Optional parameter to delete cache for specific sow names
   * @return - Result of hte cleanup operation
   */
  def deleteNedapCache(names: Option[String] = None) = tenantActionBuilder.require(Access.FeedSystem.write).apply { implicit request =>
    import scalikejdbc._
    Future {
      val methodResult = request.withSession.transaction { ts =>
        import ts.implicits._

        val queryResult = {
          if (names.nonEmpty) {
            // first we check which sows and gilt exists from the user provided names
            val requestedNames = names.get.split(",").toSeq
            val existingSows: Seq[(String, Long)] = {
              sql"""select sow_nedap.sow_id, sow.sownumber from sow_nedap
               |    join sow on sow_nedap.sow_id = sow.id
               |    where sownumber in ($requestedNames) and sow.state < 7""".stripMargin
                .map(rs => rs.string("sownumber") -> rs.long("sow_id"))
                .list
                .apply()
            }
            val existingGilts: Seq[(String, Long)] = {
              sql"""select gilt_nedap.gilt_id, gilt.sownumber from gilt_nedap
                   |join gilt on gilt_nedap.gilt_id = gilt.id
                   |where sownumber in ($requestedNames) and gilt.active is true
                 """.stripMargin
                .map(rs => rs.string("sownumber") -> rs.long("gilt_id"))
                .list
                .apply()
            }

            // delete those that exist from cache
            if (existingSows.nonEmpty) {
              sql"delete from sow_nedap where sow_id in (${existingSows.map(_._2)})".update.apply()
            }
            if (existingGilts.nonEmpty) {
              sql"delete from gilt_nedap where gilt_id in (${existingGilts.map(_._2)})".update.apply()
            }

            // make lists of existing and missing names
            val existingNames = existingSows.map(_._1).toSet ++ existingGilts.map(_._1).toSet
            val missingSowNames = requestedNames.toSet -- existingNames

            // localize the result message with the missing names (if any)
            if (missingSowNames.nonEmpty) {
              ts.serverContext.localize(
                "js.nedap.cacheDeletedSpecificWithMissing",
                if (existingNames.isEmpty) "-" else existingNames.mkString(", "),
                missingSowNames.mkString(", "),
              )
            } else {
              ts.serverContext.localize(
                "js.nedap.cacheDeletedSpecific",
                if (existingNames.isEmpty) "-" else existingNames.mkString(", "),
              )
            }
          } else {
            sql"delete from sow_nedap".update.apply()
            sql"delete from gilt_nedap".update.apply()

            ts.serverContext.localize("js.nedap.cacheDeleted")
          }
        }

        queryResult
      }
      Ok(methodResult)
    }
  }

  def callback(code: String, farmId: Long, systemId: Long) = Action.async { request =>
    val lang = request.messages.lang
    val nedapUrl = configuration.get[String]("nedap.token.url")
    val clientId = configuration.get[String]("nedap.client.id")
    val clientSecret = configuration.get[String]("nedap.client.secret")

    val redirectUrl = {
      if (request.host.contains("localhost")) {
        "urn:ietf:wg:oauth:2.0:oob"
      } else {
        s"https://${request.host}${request.path}?farmId=$farmId&systemId=$systemId"
      }
    }

    val accessTokenRequestData = Json.obj(
      "code" -> code,
      "client_id" -> clientId,
      "client_secret" -> clientSecret,
      "redirect_uri" -> redirectUrl,
      "grant_type" -> "authorization_code",
    )
    val timeZoneName = db.jooqWithTranscation { db =>
      db.select(ORGANIZATION.TIMEZONENAME).from(ORGANIZATION).where(ORGANIZATION.ID.eq(farmId)).fetchOne.value1
    }
    val processResponse = (response: WSResponse) => {
      logger.warn("Json: {}", Json.prettyPrint(response.json))
      logger.warn("status: {}", response.status)

      response.status match {
        case 200 =>
          // TODO
          nedapDbUtils.persistNedapCredentials(FeedSystemContext(farmId, timeZoneName, systemId), response.body) match {
            case Success(oAuthData) =>
              actorSystem.actorOf(
                Props(nedapFeedSystemActorFactory(FeedSystemContext(farmId, timeZoneName, systemId), oAuthData, lang)),
                s"nedap-feed-system-${farmId}-${systemId}",
              )
              true
            case Failure(ex) =>
              false
          }
        case another =>
          val result = read[OAuthError](response.body)
          logger.error("Getting error result for oauth access token read with status {}: {}", another, result)
          false
      }
    }

    val errorPage = (ex: Option[Throwable]) => {
      val errorCode = UUID.randomUUID().toString
      ex match {
        case Some(e) => logger.error(
            "Unexpected error {} when trying to get access token for farm {} and feed system {}.",
            errorCode,
            long2Long(farmId),
            long2Long(systemId),
            e,
          )
        case None => logger.error(
            "Error {} when trying to get access token for farm {} and feed system {}.",
            errorCode,
            long2Long(farmId),
            long2Long(systemId),
          )
      }
      Ok(views.html.error(errorCode, messagesApi.preferred(request)))
    }

    nedapWS.client.url(nedapUrl).post(accessTokenRequestData) map { processResponse(_) } map {
      if (_) Ok(HtmlUtils.CloseWindowHtml).as("text/html") else errorPage(None)
    } recover {
      case ex =>
        errorPage(Some(ex))
    }
  }
}
