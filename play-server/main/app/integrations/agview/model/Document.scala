/**
 * AgView Manager API
 *  Welcome to the API documentation for AgView Manager  The `swagger-ui` view can be found [here](/api-docs/swagger/). The `ReDoc` view can be found [here](/api-docs/redoc/). The swagger YAML document can be found [here](/api-docs/swagger.yaml).  To try the API on this page follow the steps below: 1. Execute the `/auth/org-token/` endpoint found under the Authentication section 2. Copy the JWT access token from the response 3. Click the Authorize green button found below these instructions 4. In the value field, type `Bearer` followed by a space and then paste the JWT access token 5. Click the Authorize green button found in the pop up 6. Execute the desired endpoint
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package integrations.agview.model

import java.time.OffsetDateTime

case class Document(
  id: Option[Int] = None,
  uploadedBy: Option[String] = None,
  organization: Option[String] = None,
  uploadedDate: Option[OffsetDateTime] = None,
  filename: String,
)
