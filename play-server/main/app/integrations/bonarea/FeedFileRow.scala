package integrations.bonarea

import com.cloudfarms.pigs.sync.Utils.CsvLineReader
import org.apache.commons.lang3.time.{ FastDateFormat, FastDateParser }
import org.jfree.data.io.CSV

import java.time.{ LocalDate, ZoneId }
import java.time.format.DateTimeFormatter
import scala.util.Try

case class FeedFileRow(
  invoiceNumber: String,
  date: LocalDate,
  batch: String,
  farmId: String,
  description: String,
  feedName: String,
  species: String,
  consumption: Double,
  pricePerUnit: Double,
  totalPrice: Double,
  proteins: Option[Double],
  phosphorus: Option[Double],
)

object FeedFileRow {

  object ParseLocalDate {
    val df = DateTimeFormatter.ofPattern("dd/MM/yyyy")
    def unapply(str: String): Option[LocalDate] =
      Try(LocalDate.parse(str, df)).toOption
  }

  object ParseDouble {
    def unapply(str: String): Option[Double] = {
      // Note: Sometimes in the file we encounter double values separated with a pipe. We will take the left value
      // Decimal comma, thousands separated with a dot
      Try(str.split('|').head.replace(".", "").replace(",", ".").toDouble).toOption
    }
  }

  object ParseOptionalDouble {
    def unapply(str: String): Option[Option[Double]] = {
      str match {
        case ""             => Some(None)
        case ParseDouble(d) => Some(Some(d))
        case _              => None
      }
    }
  }

  def unapply(line: String): Option[FeedFileRow] = {
    val parts: Array[String] = line.split(';')
    parts match {
      case Array(
            invoiceNumber,
            ParseLocalDate(date),
            batch,
            farmId,
            description,
            feedName,
            species,
            ParseDouble(consumption),
            ParseDouble(pricePerUnit),
            ParseDouble(totalPrice),
            ParseOptionalDouble(proteins),
            ParseOptionalDouble(phosphorus),
            _ @_*,
          ) => Some(FeedFileRow(
          invoiceNumber,
          date,
          batch,
          farmId,
          description,
          feedName,
          species,
          consumption,
          pricePerUnit,
          totalPrice,
          proteins,
          phosphorus,
        ))
      case _ => None
    }
  }
}
