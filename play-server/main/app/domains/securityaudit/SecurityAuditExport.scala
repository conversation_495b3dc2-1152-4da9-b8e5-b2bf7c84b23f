package domains.securityaudit

import javax.inject.{ Inject, Singleton }
import domains.common.grid2.{ ExportService, ServerContextBuilder }
import play.api.mvc._
import pushka.json._
import reactapp.shared.auth.Access
import reactapp.shared.securityaudit.SecurityAuditGrid
import sjsgrid.shared.grid.column.ColumnsState
import sjsgrid.shared.grid.dto.RowsToGet
import tenant.{ SessionProvider, WebActionBuilder }

@Singleton
class SecurityAuditExport @Inject() (
  securityAuditService: SecurityAuditService,
  tenantActionBuilder: WebActionBuilder,
  exportService: ExportService,
  cc: ControllerComponents,
)(implicit sessionProvider: SessionProvider, serverContextBuilder: ServerContextBuilder)
    extends AbstractController(cc) {
  def exportData(columnsState: String): Action[AnyContent] = {
    tenantActionBuilder.require(Access.SecurityAudit.read).sync { implicit request =>
      request.withSession.transaction { ts =>
        import ts.implicits._

        val columnsStateObj: ColumnsState[SecurityAuditGrid] = read[ColumnsState[SecurityAuditGrid]](columnsState)

        exportService
          .exportVirtualPaging(
            sql = securityAuditService.getListSql(columnsStateObj.toDataGetterParams(RowsToGet.AllRows)),
            count = securityAuditService.countInternal(columnsStateObj.filteringAndSorting),
            gridDefinition = SecurityAuditGrid,
            columnsPositions = columnsStateObj.positions,
          )
      }
    }
  }
}
