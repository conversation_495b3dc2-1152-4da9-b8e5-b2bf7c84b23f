package domains.portlets.reportingapp

import akka.actor.PoisonPill
import akka.event.LoggingReceive
import controllers.ws.feed.ActorWithRestartLogging
import domains.portlets.PortletsDefinitionService
import domains.portlets.reportingapp.ReportingAppActor.Command._
import domains.portlets.reportingapp.ReportingAppActor._
import domains.portlets.reportingapp.ReportingAppActor.OperationType._
import org.apache.commons.lang3.time.DurationFormatUtils
import play.api.Configuration
import tenant.FarmAndLang
import zio._
import zio.stream.ZStream

import java.io.IOException
import java.time.Instant
import javax.inject.Inject

class ReportingAppActor @Inject() (
  reportingAppMongoClientApi: ReportingAppMongoClientApi,
  portletsDefinitionService: PortletsDefinitionService,
  synchronizer: MongoDataSynchronizer,
  configuration: Configuration,
) extends ActorWithRestartLogging {

  type WakeUpWorkerTask = Task[Unit]
  private val actorInstanceId: Long = Instant.now().toEpochMilli

  /**
    * Start the process of portlet synchronization:
    *   1. Before start synchronize all Reports and Portlets
    *   1. Start endless async processing of PortletsData by independent "workers"
    *
    * Number of workers is calculated as twice the number of "portlets parallelization" (2 * "portletCalcPar")
    * to utilize full potential of synchronization.
    * It does not use real threads but [[zio.Fiber]]s which are virtual threads.
    */
  private val (workers, wakeUpWorkerTasksRef) = runZIO(
    for {
      _ <- logDebugZio(PreStartSync)
      _ <- ZIO.fromFuture(implicit ec => synchronizer.syncReportsAndPortlets())

      _ <- logDebugZio(StartDataProcessing)
      wakeUpWorkerTasksRef <- Ref.make(Set.empty[WakeUpWorkerTask])
      parallelism = configuration.getOptional[Int]("portlets.portletCalcPar").getOrElse(8) * 2
      workerFibers <- ZIO.collectAll(
        (1 to parallelism).map(id => createWorker(id, wakeUpWorkerTasksRef).forever.forkDaemon),
      )
    } yield (workerFibers, wakeUpWorkerTasksRef),
  )

  /** Kill all workers after Actor was shutdown. */
  override def postStop(): Unit = {
    runZIO(
      for {
        _ <- logDebugZio(ShutdownWorkers, "Actor shutdown - before kill workers")
        _ <- ZIO.foreach(workers)(_.interrupt)
        _ <- logDebugZio(ShutdownWorkers, "Actor shutdown - after kill workers")
      } yield (),
    )
  }

  override def receive: Receive = LoggingReceive {
    case SyncReport =>
      log.info(s"[actorId: $actorInstanceId]  SyncReport message accepted. Wake up all workers.")
      runZIO(
        for {
          _ <- logDebugZio(WakeUpWorkers, s"report opened in reporting app - wake up all workers")
          _ <- wakeUpAllWorkers(wakeUpWorkerTasksRef)
        } yield (),
      )

    case Shutdown =>
      log.info(s"[actorId: $actorInstanceId] Shutdown message accepted: kill actor")
      context stop self

    case PoisonPill =>
      log.info(s"[actorId: $actorInstanceId] PoisonPill message accepted: kill actor")
      context stop self

    case msg => log.error(s"[actorId: $actorInstanceId] unknown message received: $msg")
  }

  private def runZIO[E, A](zioApp: IO[E, A]): A = {
    Unsafe.unsafe(implicit unsafe => {
      zio.Runtime.default.unsafe.run(
        zioApp,
      ).getOrThrowFiberFailure()
    })
  }

  private def niceTimeFormat(ms: Long) = DurationFormatUtils.formatDuration(ms, "m:ss.SSS", true)

  /**
    * Create independent worker for processing portlets data (PortletsData collection in MongoDB)
    *
    * Worker is process of:
    *  1. Fetch portlet
    *  1. Decide
    *   a. If nothing is fetched - put worker to sleep (30sec, or until SyncReport msg is received by the Actor)
    *   a. If something is fetched
    *    1. wake up all other workers (to help this worker with possible work)
    *    1. calculate fetched portlet data
    *
    * @param workerId identification of worker, mainly used for logging
    * @param wakeUpWorkerTasksRef contains reference to set of tasks that could wake up workers
    * @return blueprint to create worker in ZIO
    */
  private def createWorker(
    workerId: Int,
    wakeUpWorkerTasksRef: Ref[Set[WakeUpWorkerTask]],
  ): Task[Unit] = {

    /**
      * Fetch next portlet data that should be recalculated if any.
      * There are 3 priority types of fetch which will be optionally called when higher priority fetch returns empty.
      */
    val fetchNextPortlet: Task[Option[FetchedPortlet]] = {
      def fetchNextPortletWithPriority(priority: ProcessPriority): Task[Option[FetchedPortlet]] = for {
        portletOpt <- ZIO.fromFuture(_ => reportingAppMongoClientApi.nextPortletToProcess(priority, actorInstanceId))
        formattedPortletName = portletOpt.map(_.toString).getOrElse(None.toString)
        waitingMsTimeUntilFetched = portletOpt.map(_.waiting.toMillis)
        formattedWaitingTimeDuration = waitingMsTimeUntilFetched
          .map(waitingMs => s"Was waiting [${niceTimeFormat(waitingMs)}].")
          .getOrElse("")
        _ <- logDebugZioWorker(
          FetchEnd,
          workerId,
          s"fetchedPortlet with $priority priority. $formattedWaitingTimeDuration Portlet: [$formattedPortletName]",
        )
      } yield portletOpt

      for {
        startTime <- ZIO.succeed(Instant.now)
        _ <- logDebugZioWorker(FetchStart, workerId)
        nextPortlet <- ZStream(ProcessPriority.High, ProcessPriority.Medium, ProcessPriority.Low)
          .mapZIO(fetchNextPortletWithPriority(_))
          .collectSome
          .runHead
        fetchDurationMillis = Duration.fromInterval(startTime, Instant.now).toMillis
        formattedFetchDuration = niceTimeFormat(fetchDurationMillis)
        _ <- logDebugZioWorker(FetchEnd, workerId, s"Fetch took: [$formattedFetchDuration]")
      } yield nextPortlet.map(p => {
        p.copy(waiting =
          p.waiting.plusMillis(fetchDurationMillis), // add fetch time to waiting time to nicely format total time until resolved
        )
      })
    }

    val SleepTimeSec = 30

    /**
      * Put the worker into sleep.
      * Called when fetch of portlets is empty.
      * It will be automatically waked up after `SleepTimeSec` seconds (set to 30 seconds now).
      * Or when Actor receives [[SyncReport]] message
      */
    val putWorkerToSleep: Task[Unit] = for {
      _ <- logDebugZioWorker(PutWorkerIntoSleep, workerId, s"Stop fetching portlets for $SleepTimeSec sec")
      promiseToWakeUp <- Promise.make[Throwable, Unit]

      // this function will complete the promise which means that worker will be waked up
      wakeUpTask = promiseToWakeUp.complete(ZIO.unit).unit

      _ <- wakeUpWorkerTasksRef.update(_ + wakeUpTask)

      // sleep 30 seconds, or until promise is fulfilled - which one is first
      _ <- ZIO.raceFirst(
        ZIO.sleep(SleepTimeSec.seconds),
        Seq(promiseToWakeUp.await),
      )
      _ <- wakeUpWorkerTasksRef.update(_ - wakeUpTask) // remove the promise from set
    } yield ()

    /**
      * Calculate fetched portlet under specific Farm and with specified language.
      *
      * @param portletKey portlet data composite key which should be calculated
      * @return blueprint of calculation portlet process in ZIO which returns pair of portlet key and either of error or nothing when success
      */
    def calculatePortlet(portlet: FetchedPortlet): IO[IOException, (PortletsDataCompositeKey, Either[Throwable, Unit])] = for {
      startTime <- ZIO.succeed(Instant.now)
      portletKey = portlet.key
      _ <- logDebugZioWorker(CalcStart, workerId, s"Start portlet calculation [$portletKey]")
      farmAndLangContext = FarmAndLang(portletKey.farmId, portletKey.langCode)
      calcRes <- ZIO.fromFuture(implicit ec =>
        portletsDefinitionService.recomputePortlet(portletKey, actorInstanceId)(farmAndLangContext),
      ).either.map(e => portletKey -> e)
      calcDurationMillis = Duration.fromInterval(startTime, Instant.now).toMillis
      formattedCalcDuration = niceTimeFormat(calcDurationMillis)
      formattedTotalDuration = niceTimeFormat(portlet.waiting.toMillis + calcDurationMillis)

      _ <- logDebugZioWorker(
        CalcEnd,
        workerId,
        s"End portlet calculation [$portletKey]. It took [$formattedCalcDuration] TOTAL TIME: [$formattedTotalDuration]",
      )
    } yield calcRes

    def onCalculationError(portletKeyAndResult: (PortletsDataCompositeKey, Either[Throwable, Unit])): UIO[Unit] = {
      val (portletKey, calcResult) = portletKeyAndResult._1 -> portletKeyAndResult._2
      ZIO.succeed(
        calcResult.left.foreach { ex =>
          val msg = s"[actorId: $actorInstanceId] [WORKER: $workerId] failed to calculate portlet: $portletKey"
          log.error(ex, msg)
        },
      )
    }

    // main worker process
    for {
      fetchedPortlet <- fetchNextPortlet

      _ <- fetchedPortlet match {
        case None => putWorkerToSleep
        case Some(portlet) => for {
            _ <- logDebugZioWorker(WakeUpWorkers, workerId, s"Worker fetched some portlet to process. Wake up all workers to help him.")
            _ <- wakeUpAllWorkers(wakeUpWorkerTasksRef)
            _ <- calculatePortlet(portlet).map(onCalculationError)
          } yield ()
      }
    } yield ()
  }

  private def logDebugZio(operation: OperationType, text: String = ""): UIO[Unit] = {
    val msg = s"[actorId: $actorInstanceId] zioSync [$operation] $text"
    ZIO.succeed(log.debug(msg)) // TODO: replace with ZIO.log?
  }

  private def logDebugZioWorker(operation: OperationType, workerId: Long, text: String = ""): UIO[Unit] = {
    val msg = s"[actorId: $actorInstanceId] [WORKER: ${f"$workerId%02d"}] zioSyncWorker [$operation] $text"
    ZIO.succeed(log.debug(msg)) // TODO: replace with ZIO.log?
  }

  /**
    * Wake all workers up from sleep.
    * Usually called when user open some report in reporting app
    * and therefore [[SyncReport]] message is delivered to this Actor.
    * Or when some of workers successfully fetch some report - to make currently sleeping workers helpful with potential work.
    *
    * @param wakeUpWorkerTasksRef contains reference to set of tasks that could wake up workers
    */
  private def wakeUpAllWorkers(wakeUpWorkerTasksRef: Ref[Set[WakeUpWorkerTask]]): Task[Unit] = for {
    wakeUpWorkerTasks <- wakeUpWorkerTasksRef.get
    _ <- logDebugZio(WakeUpWorkers, s"Wake up all workers (sleeping workers count: ${wakeUpWorkerTasks.size})")
    _ <- ZIO.collectAllParDiscard(wakeUpWorkerTasks) // run all tasks to wake up all workers
    _ <- logDebugZio(WakeUpWorkers, s"Wake up all workers (sleeping workers count: ${wakeUpWorkerTasks.size})")
  } yield ()

}

object ReportingAppActor {

  sealed trait Command
  object Command {
    case object SyncReport extends Command
    case object Shutdown extends Command

  }

  sealed trait OperationType
  object OperationType {
    case object PreStartSync extends OperationType
    case object StartDataProcessing extends OperationType
    case object FetchStart extends OperationType
    case object FetchEnd extends OperationType
    case object CalcStart extends OperationType
    case object CalcEnd extends OperationType
    case object PutWorkerIntoSleep extends OperationType
    case object WakeUpWorkers extends OperationType
    case object ShutdownWorkers extends OperationType
  }
}
