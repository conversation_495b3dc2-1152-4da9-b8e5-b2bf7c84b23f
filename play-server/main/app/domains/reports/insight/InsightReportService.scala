package domains.reports.insight

import controllers.pdf.PdfUtils
import domains.common.SqlFilters
import domains.kpi.KpiDefinitionService
import domains.view.ViewService
import models.handlers.SettingsHandler
import reactapp.shared.kpi.{ Kpi, KpiConversions }
import reactapp.shared.reports.insight.InsightKpi.StaticKpiDefinition
import reactapp.shared.reports.insight.InsightReportData.Content.{ KpiAndValues, ThreeDimensions, TwoDimensions }
import reactapp.shared.reports.insight.InsightReportData.{ Header, KpiWithDecimal, Row }
import reactapp.shared.reports.insight.StaticKpi.{ DEAD_BORNS, DEAD_BORNS_PER_LITTER, LIVE_BORNS, LIVE_BORNS_PER_LITTER }
import reactapp.shared.reports.insight._
import reactapp.shared.reports.insight.sowinsight.InsightReportApi
import reactapp.shared.view.ViewId
import reports.kpi.KpiUtilsDI
import reports.kpi.calculators.KpisCalculator
import reports.kpi.calculators.data.analysis.InsideReportKpisDataSourceBuilder
import security.ApplicationUser
import sjsgrid.shared.common.dto.{ DateEntry, FilterRange, NumberRange }
import slogging.StrictLogging
import tenant.{ AutowireContext, SessionProvider }

import javax.inject.{ Inject, Singleton }
import scala.util.{ Failure, Success, Try }
import utils.ScalaUtils.MapOps
import domains.common.ScalikeOps._
import domains.common.grid2.ServerContextBuilder
import domainz.common.ResourceScope
import play.api.i18n.Lang
import scalikejdbc.DBSession
import reports.kpi.calculators.data.KpisDataSourceService
import java.sql.Connection
import java.text.Format

/**
  * Created by Milan Satala
  * Date: 10/24/16
  * Time: 2:00 PM
  */
@Singleton
class InsightReportService @Inject() (
  viewService: ViewService,
  kpiDefinitionService: KpiDefinitionService,
  kpisDataSourceService: KpisDataSourceService,
  settingsHandler: SettingsHandler,
)(implicit sessionProvider: SessionProvider, serverContextBuilder: ServerContextBuilder) extends StrictLogging {
  def withTenant(t: AutowireContext) = new InsightReportApiImpl(t)

  // TODO: DBSPLIT - chose the DB based on the scope of the view!
  def getInsightReports(reportType: InsightReportType)(implicit
    session: DBSession,
    user: ApplicationUser,
  ): Map[ResourceScope, Map[String, InsightReportDefinition]] = {
    viewService.getView[InsightReportDefinition](screenView(reportType)).scopesMap.mapValuesStrict(_.views.map(view =>
      view.name -> view.data,
    ).toMap)
  }

  def getInsightReportIds(reportType: InsightReportType)(implicit session: DBSession, user: ApplicationUser): Seq[ViewId] =
    viewService.getView[InsightReportDefinition](screenView(reportType)).viewIds.toSeq
  def screenView(reportType: InsightReportType) = s"${InsightReportService.Prefix}-${reportType.entryName}"

  class InsightReportApiImpl(t: AutowireContext) extends InsightReportApi {

    private def viewServiceApi = new viewService.ViewApiImpl(t)

    def getInsightReports(reportType: InsightReportType) = t.require(reportType.read).withSession.readOnly { s =>
      import s.implicits._
      viewService.getView[InsightReportDefinition](screenView(reportType)).scopesMap.mapValuesStrict(_.views.map(view =>
        view.name -> view.data,
      ).toMap)
    }

    def createReport(reportType: InsightReportType, reportId: ViewId, definition: InsightReportDefinition) =
      viewServiceApi.createView(screenView(reportType), reportId, pushka.write(definition))

    def updateReport(reportType: InsightReportType, reportId: ViewId, definition: InsightReportDefinition) =
      viewServiceApi.updateView(screenView(reportType), reportId, pushka.write(definition))

    def deleteReport(reportType: InsightReportType, reportId: ViewId) =
      viewServiceApi.deleteView(screenView(reportType), reportId)

    def calculateData(reportDefinition: InsightReportDefinition): Either[String, InsightReportData] = {
      t.require(InsightReportType.SomeType.read).withSession.readOnly { ts =>
        import ts.implicits._

        InsightReportService.this.calculateData(reportDefinition)
      }
    }

  }

  private def reportHeaderFormatter(implicit user: ApplicationUser, lang: Lang): PartialFunction[Any, String] = {
    lazy val dateFormatter: Format = PdfUtils.farmShortDateFormat(lang, user)

    {
      case NumberRange.Range(min, max) => s"$min..$max"
      case NumberRange.Exactly(n)      => n.toString
      case NumberRange.From(n)         => s"$n.."
      case NumberRange.To(n)           => s"..$n"
      case values: Seq[String]         => values.reduceLeft((aggr, s) => s"$aggr, $s")
      case FilterRange.Range(from: DateEntry, to: DateEntry) =>
        s"${dateFormatter.format(SqlFilters.toDate(from, true))}..${dateFormatter.format(SqlFilters.toDate(to, false))}"
      case FilterRange.Exactly(exactly: DateEntry) =>
        s"${dateFormatter.format(SqlFilters.toDate(exactly, true))}..${dateFormatter.format(SqlFilters.toDate(exactly, false))}"
      case FilterRange.From(from: DateEntry) => s"${dateFormatter.format(SqlFilters.toDate(from, true))}.."
      case FilterRange.Until(to: DateEntry)  => s"..${dateFormatter.format(SqlFilters.toDate(to, false))}"
      case FilterRange.Range(min, max)       => s"$min..$max"
      case FilterRange.Exactly(n)            => n.toString
      case FilterRange.From(n)               => s"$n.."
      case FilterRange.Until(n)              => s"..$n"

    }
  }

  private def calculate2DData(reportDefinition: InsightReportDefinition)(implicit
    user: ApplicationUser,
    conn: Connection,
    dbSession: DBSession,
    lang: Lang,
  ): InsightReportData = {

    import scala.jdk.CollectionConverters._

    val categories: Seq[Category[_, _ <: Category[_, _]]] = CategorySupport.getXCategories(reportDefinition)
    val combinedCategories = CategorySupport.getCombinedCategories(categories)
    val mainCategory = categories.filter {
      case _: Category[_, _] with MainCategoryComnomTablesProvider => true
      case _                                                       => false
    }.map(c => c.asInstanceOf[Category[_, _] with MainCategoryComnomTablesProvider]).head

    val sql = CategorySupport.createCategoryQuery(
      categories.filter {
        case mc: SubCategoryComnomTablesProvider if mc.field != mainCategory.field => true
        case _                                                                     => false
      } map { c => c.asInstanceOf[Category[_, _] with SubCategoryComnomTablesProvider] },
      mainCategory,
      reportDefinition,
    )

    CategorySupport.persistCategory(sql)
    val weightUnit = user.farmSettings().weight
    val kpiCalculator = new KpisCalculator(
      kpiDefinitionService.getKpiDefinitionsJavaMap,
      kpisDataSourceService.getKpisDataSourceForSowAnalysis(
        user,
        conn,
        new InsideReportKpisDataSourceBuilder(
          null,
          null,
          settingsHandler,
          user,
          conn,
        ).withOrganizations(
          Map((java.lang.Long.valueOf(user.getFarmId), user.getFarmTimezoneName)).asJava,
        ),
        KpiUtilsDI.pigQualityClasses(conn, user.getFarmId),
      ),
      settingsHandler,
      user,
      conn,
    )
    val kpiDefinitions = kpiDefinitionService.getStaticKpiDefinitionsMap
    val reportKpis = reportDefinition.kpis.collect {
      case kpi: StaticKpiDefinition => (kpi, kpiDefinitions(kpi.staticKpi.entryName))
    }
    var kpisWithValues: Seq[KpiAndValues] = Nil

    for ((staticKpiDefinition, kpi) <- reportKpis) {
      val kpiValues = for {
        categoryIndex <- combinedCategories.indices
        kpiValue = kpiCalculator.calculateScala(kpi, categoryIndex).map {
          Double2double _ andThen KpiConversions.convertToOrgUnit(kpi.kpiUnit, weightUnit)
        }
      } yield kpiValue
      kpisWithValues = kpisWithValues :+ KpiAndValues(KpiWithDecimal(staticKpiDefinition, kpi.decimalPlaces), kpiValues)
    }
    InsightReportData(getReportColumnHeaders(categories, reportDefinition.groupingsX), TwoDimensions(kpisWithValues))

  }

  private def calculate3DData(reportDefinition: InsightReportDefinition)(implicit
    user: ApplicationUser,
    conn: Connection,
    dbSession: DBSession,
    lang: Lang,
  ): InsightReportData = {

    import scala.jdk.CollectionConverters._

    val categories = CategorySupport.getAllCategories(reportDefinition)
    val combinedCategories: Seq[Seq[(Category[_, _], Int)]] = CategorySupport.getCombinedCategories(categories)
    val columnCoordinates: Seq[Seq[(Category[_, _], Int)]] =
      CategorySupport.getCombinedCategories(CategorySupport.getXCategories(reportDefinition))
    val rowCoordinates: Seq[Seq[(Category[_, _], Int)]] =
      CategorySupport.getCombinedCategories(CategorySupport.getYCategories(reportDefinition))
    val mainCategory = categories.filter {
      case _: Category[_, _] with MainCategoryComnomTablesProvider => true
      case _                                                       => false
    }.map {
      _.asInstanceOf[Category[_, _] with MainCategoryComnomTablesProvider]
    }.head
    val sql = CategorySupport.createCategoryQuery(
      categories.filter {
        case mc: Category[_, _] with SubCategoryComnomTablesProvider if mc.field != mainCategory.field => true
        case _                                                                                         => false
      } map {
        _.asInstanceOf[Category[_, _] with SubCategoryComnomTablesProvider]
      },
      mainCategory,
      reportDefinition,
    )

    CategorySupport.persistCategory(sql)
    val kpiCalculator = new KpisCalculator(
      kpiDefinitionService.getKpiDefinitionsJavaMap,
      kpisDataSourceService.getKpisDataSourceForSowAnalysis(
        user,
        conn,
        new InsideReportKpisDataSourceBuilder(
          null,
          null,
          settingsHandler,
          user,
          conn,
        ).withOrganizations(
          Map((java.lang.Long.valueOf(user.getFarmId), user.getFarmTimezoneName)).asJava,
        ),
        KpiUtilsDI.pigQualityClasses(conn, user.getFarmId()),
      ),
      settingsHandler,
      user,
      conn,
    )
    val combinedCategoriesSet: Seq[Set[(Category[_, _], Int)]] = combinedCategories.map(_.toSet)
    val kpiDefinitions = kpiDefinitionService.getStaticKpiDefinitionsMap
    val reportKpis: Seq[(StaticKpiDefinition, Kpi)] =
      reportDefinition.kpis.collect { case kpi: StaticKpiDefinition => (kpi, kpiDefinitions(kpi.staticKpi.entryName)) }
    var rows: Seq[Row] = Nil
    val weightUnit = user.farmSettings().weight
    for (rowCoordinate <- rowCoordinates) {
      val rowKpis: Seq[Seq[Option[Double]]] = for (columnCoordinate <- columnCoordinates) yield {
        for {
          (staticKpiDefinition, kpi) <- reportKpis // also for KPIs when it is provided correctly
          categoryCoordinate = combinedCategoriesSet.indexOf((columnCoordinate ++ rowCoordinate).toSet)
        } yield kpiCalculator.calculateScala(kpi, categoryCoordinate).map {
          Double2double _ andThen KpiConversions.convertToOrgUnit(kpi.kpiUnit, weightUnit)
        }
      }
      val rowNames: Seq[String] = rowCoordinate.map(i => i._1.values(i._2)).map(reportHeaderFormatter)
      rows = rows :+ Row(rowNames, rowKpis)
    }
    InsightReportData(
      getReportColumnHeaders(categories, reportDefinition.groupingsX),
      ThreeDimensions(rows, reportKpis.map(rKpi => KpiWithDecimal(rKpi._1, rKpi._2.decimalPlaces))),
    )

  }

  private def getReportColumnHeaders(categories: Seq[Category[_, _]], reportGroups: Seq[GroupingCriterion])(implicit
    user: ApplicationUser,
    lang: Lang,
  ): Seq[Header] = {
    val xGroups: Seq[Seq[String]] = for {
      gx <- reportGroups
      values <- categories.find(_.field == gx.reportField).map {
        _.values.map(reportHeaderFormatter)
      }
    } yield values
    xGroups.foldRight(Seq[Header]())((current, prev) => current.map(t => Header(t, prev)))
  }
  def calculateData(reportDefinition: InsightReportDefinition)(implicit
    user: ApplicationUser,
    conn: Connection,
    dbSession: DBSession,
    lang: Lang,
  ): Either[String, InsightReportData] = {
    reportDefinition match {
      case rd @ InsightReportDefinition(_, _, _, _, _, Nil) => Try(calculate2DData(rd)) match {
          case Success(rd) => Right(rd)
          case Failure(ex) =>
            logger.error("Unexpected error in insight report calculation process", ex)
            Left("Not supported")
        }
      case rd => Try(calculate3DData(rd)) match {
          case Success(rd) => Right(rd)
          case Failure(ex) =>
            logger.error("Unexpected error in insight report calculation process", ex)
            Left("Not supported")
        }
    }
  }

}

object InsightReportService {
  val Prefix = "insight-report"
}

private[this] object InsightReportMockData {
  def mockData() = {
    InsightReportData(
      columns = Seq(
        new Header("1", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
        new Header("2", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
      ),
      content = new TwoDimensions(Seq.empty),
    )
  }

  def mockSingleGroupData() = {
    InsightReportData(
      columns = Seq(
        new Header("1G", Seq.empty),
        new Header("2G", Seq.empty),
        new Header("3G", Seq.empty),
        new Header("4G", Seq.empty),
        new Header("5G", Seq.empty),
        new Header("6G", Seq.empty),
        new Header("7G", Seq.empty),
        new Header("8G", Seq.empty),
      ),
      content = new TwoDimensions(
        Seq(
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3),
            Seq(None, Some(22d), Some(120d), Some(30d), None, Some(22d), Some(120d), Some(30d)),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3),
            Seq(Some(86d), Some(32d), Some(86d), Some(40d), Some(86d), Some(32d), Some(86d), Some(40d)),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3),
            Seq(Some(0d), Some(16d), Some(0d), None, Some(0d), Some(16d), Some(0d), None),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3),
            Seq(Some(40d), Some(50d), Some(40d), Some(40d), Some(40d), Some(50d), Some(40d), Some(40d)),
          ),
        ),
      ),
    )
  }

  def mockThreeXGroups() = {
    InsightReportData(
      columns = Seq(
        new Header(
          "1",
          Seq(
            new Header(
              "G1",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
            new Header(
              "G2",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
          ),
        ),
      ),
      content = new TwoDimensions(
        Seq(
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3),
            Seq(None, Some(22d), Some(120d), Some(-30d), Some(-40d), None),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3),
            Seq(Some(86d), Some(32d), Some(86d), Some(40d), Some(53d), Some(53d)),
          ),
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3), Seq(Some(-66d), Some(0d), None, Some(12d), Some(24d), Some(24d))),
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3), Seq(Some(0d), Some(60d), None, Some(55d), Some(47d), Some(66d))),
        ),
      ),
    )
  }

  def mockThreeThreeXGroups() = {
    InsightReportData(
      columns = Seq(
        new Header(
          "1",
          Seq(
            new Header(
              "G1",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),

                // ,new Header("H4", Seq.empty),
                // new Header("H5", Seq.empty)

              ),
            ),
            new Header(
              "G2",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
          ),
        ),
        new Header(
          "2",
          Seq(
            new Header(
              "G1",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
            new Header(
              "G2",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
          ),
        ),
        new Header(
          "3",
          Seq(
            new Header(
              "G1",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
            new Header(
              "G2",
              Seq(
                new Header("H1", Seq.empty),
                new Header("H2", Seq.empty),
                new Header("H3", Seq.empty),
              ),
            ),
          ),
        ),
      ),
      content = new TwoDimensions(
        Seq(
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3),
            Seq(
              None,
              Some(22d),
              Some(120d),
              Some(-30d),
              Some(-40d),
              None,
              None,
              Some(11d),
              Some(120d),
              Some(-30d),
              Some(-40d),
              None,
              None,
              Some(22d),
              Some(120d),
              Some(-30d),
              Some(-40d),
              None,
            ),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3),
            Seq(
              Some(86d),
              Some(32d),
              Some(86d),
              Some(40d),
              Some(53d),
              Some(53d),
              Some(86d),
              Some(32d),
              Some(86d),
              Some(40d),
              Some(53d),
              Some(53d),
              Some(86d),
              Some(32d),
              Some(86d),
              Some(40d),
              Some(53d),
              Some(53d),
            ),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3),
            Seq(
              Some(-66d),
              Some(0d),
              None,
              Some(12d),
              Some(24d),
              Some(24d),
              Some(-66d),
              Some(0d),
              None,
              Some(12d),
              Some(24d),
              Some(24d),
              Some(-66d),
              Some(0d),
              None,
              Some(12d),
              Some(24d),
              Some(24d),
            ),
          ),
          KpiAndValues(
            KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3),
            Seq(
              Some(0d),
              Some(60d),
              None,
              Some(55d),
              Some(47d),
              Some(66d),
              Some(0d),
              Some(60d),
              None,
              Some(55d),
              Some(47d),
              Some(66d),
              Some(0d),
              Some(60d),
              None,
              Some(55d),
              Some(47d),
              Some(66d),
            ),
          ),
        ),
        // non symetric grouping
        /* Seq( (KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3), Seq(None, Some(22d), Some(120d), Some(100d), Some(100d),
         * Some(-30d), Some(-40d), None, None, Some(11d), Some(120d), Some(-30d), Some(-40d), None, None, Some(22d), Some(120d), Some(-30d),
         * Some(-40d), None)), (KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3), Seq(Some(86d), Some(32d), Some(86d),
         * Some(100d), Some(100d), Some(40d), Some(53d), Some(53d), Some(86d), Some(32d), Some(86d), Some(40d), Some(53d), Some(53d),
         * Some(86d), Some(32d), Some(86d), Some(40d), Some(53d), Some(53d))), (KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3),
         * Seq(Some(-66d), Some(0d), None, Some(100d), Some(100d), Some(12d), Some(24d), Some(24d), Some(-66d), Some(0d), None, Some(12d),
         * Some(24d), Some(24d), Some(-66d), Some(0d), None, Some(12d), Some(24d), Some(24d))),
         * (KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3), Seq(Some(0d), Some(60d), None, Some(100d), Some(100d), Some(55d), Some(47d),
         * Some(66d), Some(0d), Some(60d), None, Some(55d), Some(47d), Some(66d), Some(0d), Some(60d), None, Some(55d), Some(47d),
         * Some(66d))) ) */
      ),
    )
  }

  def mockDoubleGroupData() = {
    InsightReportData(
      columns = Seq(
        new Header("A", Seq(new Header("G1", Seq.empty), new Header("G2", Seq.empty))),
        new Header("B", Seq(new Header("G1", Seq.empty), new Header("G2", Seq.empty))),
      ),
      content = new TwoDimensions(
        Seq(
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3), Seq(None, Some(22d), Some(120d), Some(30d))),
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3), Seq(Some(86d), Some(-32d), Some(86d), Some(40d))),
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3), Seq(Some(0d), Some(16d), Some(0d), None)),
          KpiAndValues(KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3), Seq(Some(40d), Some(50d), Some(40d), Some(40d))),
        ),
      ),
    )
  }

  def mockTripleGroupedData() = InsightReportData(
    columns = Seq(
      new Header("1", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
      new Header("2", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
      new Header("3", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
      new Header("4", Seq(new Header("A", Seq.empty), new Header("B", Seq.empty))),
    ),
    content = ThreeDimensions(
      rows = Seq(
        Row(
          headerNames = Seq("G1"),
          values = Seq(Seq(None, None, None), Seq(None, None, None), Seq(None, Some(40), None), Seq(None, None, None)),
        ),
        Row(
          headerNames = Seq("G2"),
          values = Seq(
            Seq(Some(111), Some(-40), Some(30)),
            Seq(None, None, None),
            Seq(Some(40), Some(70), Some(-120)),
            Seq(None, None, None),
            Seq(None, None, None),
            Seq(None, Some(45), Some(120)),
          ),
        ),
        Row(
          headerNames = Seq("G3"),
          values = Seq(Seq(None, None, None), Seq(Some(99), Some(0), Some(16)), Seq(None, None, None), Seq(Some(44), Some(33), Some(120))),
        ),
      ),
      kpis = Seq(
        KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3),
        KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS), 3),
        KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3),
      ),
    ),
  )

  def mockMultipleGroupedData() = InsightReportData(
    columns =
      Seq(new Header(
        "MAIN",
        Seq(
          new Header(
            "1",
            Seq(
              new Header(
                "A",
                Seq(
                  new Header("@", Seq.empty),
                  new Header("#", Seq.empty),
                ),
              ),
              new Header(
                "B",
                Seq(
                  new Header("@", Seq.empty),
                  new Header("#", Seq.empty),
                ),
              ),
            ),
          ),
          new Header(
            "2",
            Seq(
              new Header(
                "A",
                Seq(
                  new Header("@", Seq.empty),
                  new Header("#", Seq.empty),
                ),
              ),
              new Header(
                "B",
                Seq(
                  new Header("@", Seq.empty),
                  new Header("#", Seq.empty),
                ),
              ),
            ),
          ),
        ),
      )),
    content = TwoDimensions(
      Seq(
        KpiAndValues(KpiWithDecimal(StaticKpiDefinition(DEAD_BORNS_PER_LITTER), 3), List.fill(8)(None)),
        KpiAndValues(KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS_PER_LITTER), 3), List.fill(8)(None)),
        KpiAndValues(KpiWithDecimal(StaticKpiDefinition(LIVE_BORNS), 3), List.fill(8)(None)),
      ),
    ),
  )
}
