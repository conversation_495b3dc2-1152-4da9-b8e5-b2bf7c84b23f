package domains.reproduction.testteams

import javax.inject.Singleton
import controllers.ws.feed.GiltFeedConsumptionUtils
import domains.common.ScalikeOps
import domains.common.grid2.{ DBModelRow, ServerContext, ServerContextBuilder, TimeZoneUtil }
import org.scalactic.{ Every, Or }
import reactapp.shared.auth.Access
import reactapp.shared.breeding.PigTestingTestteamsGrid
import reactapp.shared.reproduction.breeding.{ DWGFCRGraphData, GraphData, WeightGraphData }
import reactapp.shared.reproduction.testteams.{ PigTestingTestteamsApi, PigTestingTestteamsHistoryGrid, PigTestingTestteamsOverviewGrid }
import scalikejdbc._
import security.ApplicationUser
import sjsgrid.shared.common.dto.BusinessException
import sjsgrid.shared.grid.dto.{ DataGetterParams, FilteringAndSorting, RowSaveDTO, ServerProblem }
import tenant.{ AutowireContext, SessionProvider }
import utils.api.TenantMessages

import java.sql.Timestamp
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Inject
//import scala.util.parsing.json.JSON

@Singleton
class PigTestingTestteamsService @Inject() (
  messages: TenantMessages,
  giltFeedConsumptionUtils: GiltFeedConsumptionUtils,
  scalikeOps: ScalikeOps,
)(implicit sessionProvider: SessionProvider, serverContextBuilder: ServerContextBuilder) {
  import scalikeOps._

  private def dataByGiltAndId(testteamId: Long, tz: String) = {
    sqls"""
      |select
      |  fg.actor_date feedDate,
      |  avg(fg.weight) weight,
      |  avg(fg.feed_intake) feed_intake,
      |  count(fg.gilt_id)
      |from
      |  gilt g
      |  join feedconsumption_gilt fg
      |    on fg.gilt_id = g.id
      |       and fg.actor_date between coalesce(cast(g.teststart at time zone $tz as date), fg.actor_date) and coalesce(g.testeduntil, fg.actor_date)
      |where
      |  g.testteam_id = $testteamId
      |group by fg.actor_date
      |order by fg.actor_date
    """.stripMargin
  }

  class PigTestingTestteamsServiceImpl(t: AutowireContext) extends PigTestingTestteamsApi {
    val tz: String = t.user.getFarmTimezoneName
    val orgId: Long = t.user.getFarmId

    private def getWeightIntakeGraphData(testteamId: Long)(implicit session: DBSession): Seq[WeightGraphData] = {
      sql"${dataByGiltAndId(testteamId, tz)}".map(d => {
        WeightGraphData(
          d.date("feedDate").getTime,
          d.doubleOpt("feed_intake"),
          d.doubleOpt("weight"),
        )
      }).list.apply()
    }

    private def getFCRDWG(testteamId: Long)(implicit session: DBSession) = {
      sql"""
        |with bbb as (
        |  ${dataByGiltAndId(testteamId, tz)}
        |)
        |select
        |  after.feedDate,
        |  (after.weight - before.weight) * 1000 daily_weight_gain,
        |  after.feed_intake / nullif((after.weight - before.weight) * 1000, 0) feed_conversion_rate
        |from bbb before
        |join bbb after on before.feedDate = after.feedDate - 1
        |where nullif(after.feed_intake, 0) is not null;
      """.stripMargin.map(d => {
        DWGFCRGraphData(
          d.date("feedDate").getTime,
          d.doubleOpt("daily_weight_gain"),
          d.doubleOpt("feed_conversion_rate"),
        )
      }).list.apply()
    }

    override def getGraphData(testteamId: Long): GraphData = t.require(Access.PigTesting.read).withSession.readOnly { implicit ts =>
      implicit val dbSession: DBSession = ts.dbSession

      val fcrdwg = getFCRDWG(testteamId)
      val weightIntake = getWeightIntakeGraphData(testteamId)
      GraphData(weightIntake, fcrdwg)
    }

    override def getTestTeamGrid(
      params: DataGetterParams[PigTestingTestteamsGrid],
      animalIdOpt: Option[String],
    ): Seq[(Long, PigTestingTestteamsGrid.Row)] = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      val query = getTestTeamSql(params, animalIdOpt)
      query.map(rs => rs.long("id") -> DBModelRow.visibleColumns(PigTestingTestteamsGrid, params.columnsHiddenByUser)(rs)).list.apply()
    }

    private def generateFilters(filteringAndSorting: FilteringAndSorting[PigTestingTestteamsGrid], animalIdOpt: Option[String])(implicit
      user: ApplicationUser,
    ) = {
      animalIdOpt match {
        case Some(animalId) =>
          filteringAndSorting.filters.toSqlsNew :+ sqls"tt.id in (select testteam_id from gilt where animalid LIKE ${animalId + "%"} and testteam_id is not null)"
        case None => filteringAndSorting.filters.toSqlsNew
      }
    }

    def getTestTeamSql(params: DataGetterParams[PigTestingTestteamsGrid], animalIdOpt: Option[String])(implicit
      user: ApplicationUser,
      serverContext: ServerContext,
    ): SQL[Nothing, NoExtractor] = {
      val filtersWithAnimalFilter = generateFilters(params.filteringAndSorting, animalIdOpt)
      sql"""
        |with sum_feed_intake as(
        |  select
        |    g.id id,
        |    sum(fg.feed_intake) sum
        |  from
        |    gilt g
        |    join feedconsumption_gilt fg
        |      on fg.gilt_id = g.id
        |         and fg.actor_date between coalesce(cast(g.teststart at time zone $tz as date), fg.actor_date) and coalesce(g.testeduntil, fg.actor_date)
        |  where g.testendweight is not null
        |  group by g.id
        |)
        |select
        |  tt.id,
        |  ${PigTestingTestteamsGrid.visibleColumnsSql(params.columnsHiddenByUser)}
        |from organization o cross join
        |  testteam tt
        |  join location l on l.id = tt.location_id
        |  left outer join gilt g on tt.id = g.testteam_id
        |  left outer join sum_feed_intake sfi on sfi.id = g.id
        |  ${params.filteringAndSorting.joinsSqlNew}
        |where o.id = get_tenant_organization() ${andAll(filtersWithAnimalFilter)}
        |group by
        |  o.id,
        |  tt.id,
        |  l.externalname,
        |  ${params.filteringAndSorting.sortings.groupByColumnsSql()}

        |${havingAll(params.filteringAndSorting.filters.toHavingSqlsNew)}
        |${params.filteringAndSorting.sortings.toSqlNew(sqls"tt.id")}
        |${params.rowsToGet.toSqls};
      """.stripMargin
    }

    override def countRows(filteringAndSorting: FilteringAndSorting[PigTestingTestteamsGrid], animalIdOpt: Option[String]): Int = {
      t.require(Access.PigTesting.read).withSession.readOnly { ts =>
        import ts.implicits._

        val filtersWithAnimalFilter = generateFilters(filteringAndSorting, animalIdOpt)
        sql"""
        |with sum_feed_intake as (
        |  select
        |    g.id id,
        |    sum(fg.feed_intake) sum
        |  from
        |    gilt g
        |    join feedconsumption_gilt fg
        |      on fg.gilt_id = g.id
        |         and fg.actor_date between coalesce(cast(g.teststart at time zone $tz as date), fg.actor_date) and coalesce(g.testeduntil, fg.actor_date)
        |  where g.testendweight is not null
        |  group by g.id
        |)
        |select
        |   distinct count(1) over ()
        |from organization o cross join
        |  testteam as tt
        |  join location as l on l.id = tt.location_id
        |  left outer join gilt g on tt.id = g.testteam_id
        |  left outer join sum_feed_intake sfi on sfi.id = g.id
        |  ${filteringAndSorting.joinsSqlNew}
        |where o.id = get_tenant_organization() ${andAll(filtersWithAnimalFilter)}
        |group by
        |  o.id,
        |  tt.id,
        |  l.externalname

        |${havingAll(filteringAndSorting.filters.toHavingSqlsNew)}
        """.stripMargin.map(_.int(1)).single.apply().getOrElse(0)
      }
    }

    override def getWeightIntakeHistoryData(
      testteamId: Long,
      params: DataGetterParams[PigTestingTestteamsHistoryGrid],
    ): Seq[(Long, PigTestingTestteamsHistoryGrid.Row)] = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      getWeightIntakeSql(testteamId, params).map(rs =>
        rs.long("id") -> DBModelRow.visibleColumns(PigTestingTestteamsHistoryGrid, params.columnsHiddenByUser)(rs),
      ).list.apply()
    }

    def getWeightIntakeSql(testteamId: Long, params: DataGetterParams[PigTestingTestteamsHistoryGrid])(implicit
      user: ApplicationUser,
      serverContext: ServerContext,
    ): SQL[Nothing, NoExtractor] = {
      val filtersWithTestteamFilter =
        params.filteringAndSorting.filters.toSqlsNew :+ sqls"g.testteam_id = $testteamId"
      sql"""
        |select
        |  fn.id,
        |  ${PigTestingTestteamsHistoryGrid.visibleColumnsSql(params.columnsHiddenByUser)}
        |from
        |  feedconsumption_nedap fn
        |  join gilt g
        |    on fn.gilt_id = g.id
        |       and cast(fn.feeding_start at time zone $tz as date) between
        |         cast(coalesce(g.teststart, fn.feeding_start) at time zone $tz as date)
        |         and
        |         coalesce(g.testeduntil, cast(fn.feeding_start at time zone $tz as date))
        |  left outer join feed_station fs on fn.feedstation_id = fs.id
        |${whereAll(filtersWithTestteamFilter)}
        |${params.filteringAndSorting.sortings.toSqlNew(sqls"fn.id")}
        |${params.rowsToGet.toSqls};
      """.stripMargin
    }

    override def getWeightIntakeHistoryCount(
      testteamId: Long,
      filteringAndSorting: FilteringAndSorting[PigTestingTestteamsHistoryGrid],
    ): Int = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      val filtersWithTestteamFilter =
        filteringAndSorting.filters.toSqlsNew :+ sqls"g.testteam_id = $testteamId"
      sql"""
        |select
        |  count(1)
        |from feedconsumption_nedap fn
        |  join gilt g
        |    on fn.gilt_id = g.id
        |       and cast(fn.feeding_start at time zone $tz as date) between
        |         cast(coalesce(g.teststart, fn.feeding_start) at time zone $tz as date)
        |         and
        |         coalesce(g.testeduntil, cast(fn.feeding_start at time zone $tz as date))
        |  left outer join feed_station fs on fn.feedstation_id = fs.id
        |${whereAll(filtersWithTestteamFilter)}
      """.stripMargin.count
    }

    override def getOverviewData(testteamId: Long): Seq[(Long, PigTestingTestteamsOverviewGrid.Row)] = {
      t.require(Access.PigTesting.read).withSession.readOnly { ts =>
        import ts.implicits._

        sql"""
        |with sum_feed_intake as (
        |  select
        |     g.id gilt_id,
        |     sum(fg.feed_intake) sum
        |  from
        |    gilt g
        |    join feedconsumption_gilt fg on fg.gilt_id = g.id
        |         and fg.actor_date between coalesce(cast(g.teststart at time zone $tz as date), fg.actor_date) and coalesce(g.testeduntil, fg.actor_date)
        |  where g.testteam_id = $testteamId
        |  group by g.id
        |)
        |select
        |  gilt.id,
        |  ${PigTestingTestteamsOverviewGrid.allColumnsSql}
        |from
        |  gilt
        |  left outer join breed on breed.animalid = gilt.animalid
        |  left outer join sum_feed_intake sfi on sfi.gilt_id = gilt.id
        |  left outer join
        |    (
        |       select
        |         gilt_id,
        |         last(strength1 ORDER BY hop.actor_date) strength1,
        |         last(strength2 ORDER BY hop.actor_date) strength2,
        |         last(strength3 ORDER BY hop.actor_date) strength3,
        |         last(strength4 ORDER BY hop.actor_date) strength4,
        |         last(backfat1 ORDER BY hop.actor_date) backfat1,
        |         last(backfat2 ORDER BY hop.actor_date) backfat2,
        |         last(backfat3 ORDER BY hop.actor_date) backfat3,
        |         last(backfat4 ORDER BY hop.actor_date) backfat4
        |       from backfat
        |         join hop on hop.id = backfat.id
        |         join gilt on gilt.id = hop.gilt_id
        |       where
        |         gilt_id is not null
        |         and gilt.testteam_id = $testteamId
        |       group by gilt_id
        |    ) sb on sb.gilt_id = gilt.id
        |where
        |  gilt.testteam_id = $testteamId
      """.stripMargin.map(rs => rs.long("id") -> DBModelRow.allColumns(PigTestingTestteamsOverviewGrid)(rs)).list.apply()
      }
    }

    override def saveHistory(
      testteamId: Long,
      historyData: Seq[RowSaveDTO[PigTestingTestteamsHistoryGrid, Option[Long]]],
    ): Map[Int, Long Or Every[ServerProblem]] = t.require(Access.PigTesting.write).apply { vt =>
      historyData.map { rowSaveDTO =>
        rowSaveDTO.rowId -> vt.withSession.resultOrProblems(rowSaveDTO.acceptedWarnings) { context =>
          import context.ts.implicits._
          val row =
            rowSaveDTO.validateOrThrow(PigTestingTestteamsHistoryGrid)(PigTestingTestteamsHistoryGrid.ValidationProps(rowSaveDTO.metadata.nonEmpty))

          val historyIdOpt = row.metadata
          val animalId = historyIdOpt match {
            case Some(historyId) =>
              import quill.QuillContext._
              run(
                for {
                  fn <- query[qfarm.FeedconsumptionNedap] if fn.id == lift(historyId)
                  gilt <- query[qfarm.Gilt] if fn.giltId.contains(gilt.id)
                } yield gilt.animalid,
              ).execute().head.getOrElse(sys.error(s"Could not find gilt for feed consumption history with id $historyId"))

            case None => row.inputs(PigTestingTestteamsHistoryGrid.AnimalId).getOrThrow("AnimalId is required for new history rows")
          }

          val feedingStart = row.inputs(PigTestingTestteamsHistoryGrid.FeedingStart)
          val feedingEnd = row.inputs(PigTestingTestteamsHistoryGrid.FeedingEnd)
          val weight = row.inputs(PigTestingTestteamsHistoryGrid.Weight)
          val feedIntake = row.inputs(PigTestingTestteamsHistoryGrid.FeedIntake)
          val feedStationName = row.inputs(PigTestingTestteamsHistoryGrid.FeedStationName)

          val giltId = sql"""
            select id from gilt where animalid = $animalId
          """.map(rs => rs.long("id")).single.apply()
          val giltTestteamId = sql"""
            select testteam_id from gilt where animalid = $animalId
          """.map(rs => rs.long("testteam_id")).single.apply()
          val feedStationId = sql"""
            select id from feed_station where name = $feedStationName
          """.map(rs => rs.long("id")).single.apply()

          (giltId, feedStationId, historyIdOpt) match {
            case (Some(giltId), feedStationIdOpt, None) =>
              if (giltTestteamId.getOrElse(-1) != testteamId) {
                context.addError(messages("validation.error.test.wrong.testteam"))
                -1.toLong
              } else {
                sql"""
                  insert into feedconsumption_nedap (feed_intake, feeding_start, feeding_end, feedstation_id, gilt_id, weight)
                    values ($feedIntake, $feedingStart, $feedingEnd, $feedStationIdOpt, $giltId, $weight)
                """.execute.apply()
                giltFeedConsumptionUtils.recountFeedconsumptionGiltDataInTx(giltId, orgId, Some(java.util.Date.from(feedingStart)))
                -1.toLong
              }
            case (Some(giltId), feedStationIdOpt, Some(historyId)) =>
              sql"""
                  update feedconsumption_nedap set
                    feed_intake = $feedIntake,
                    feeding_start = $feedingStart,
                    feeding_end = $feedingEnd,
                    feedstation_id = $feedStationIdOpt,
                    gilt_id = $giltId,
                    weight = $weight
                  where id = $historyId
                """.execute.apply()
              giltFeedConsumptionUtils.recountFeedconsumptionGiltDataInTx(giltId, orgId, Some(java.util.Date.from(feedingStart)))
              historyId
            case (None, _, _) =>
              context.addError(messages("validation.error.test.team.animal.not.found", animalId))
              -1.toLong
          }
        }
      }
    }.toMap

    override def saveGiltsToTestteam(
      testteamId: Long,
      giltsData: Seq[RowSaveDTO[PigTestingTestteamsOverviewGrid, Long]],
    ): Map[Int, Long Or Every[ServerProblem]] = t.require(Access.PigTesting.write).apply { vt =>
      giltsData.map { rowSaveDTO =>
        rowSaveDTO.rowId -> vt.withSession.resultOrProblems(rowSaveDTO.acceptedWarnings) { context =>
          import context.ts.implicits._

          val earliestTestStartOpt = {
            sql"""
          select min(teststart) from gilt where testteam_id = $testteamId
        """.map(rs => wrappedResultSetOps(rs).instantOpt("min")).single.apply().flatten
          }

          val row = rowSaveDTO.validateOrThrow(PigTestingTestteamsOverviewGrid)(PigTestingTestteamsOverviewGrid.ValidationProps(
            earliestTestStartOpt,
            t.user.farmSettings().maxTestEntryDuration,
          ))

          val testStart = row.inputs(PigTestingTestteamsOverviewGrid.TestStart)
          val testEnd = row.inputs(PigTestingTestteamsOverviewGrid.TestEnd)
          val testResult = row.inputs(PigTestingTestteamsOverviewGrid.TestResult)
          val dgTestResult = row.inputs(PigTestingTestteamsOverviewGrid.DGTestResult)
          val testStartWeight = row.inputs(PigTestingTestteamsOverviewGrid.TestStartWeight)
          val testEndWeight = row.inputs(PigTestingTestteamsOverviewGrid.TestEndWeight)
          val breed = row.inputs(PigTestingTestteamsOverviewGrid.Breed)

          val strength1Opt = row.inputs(PigTestingTestteamsOverviewGrid.Strength1)
          val strength2Opt = row.inputs(PigTestingTestteamsOverviewGrid.Strength2)
          val strength3Opt = row.inputs(PigTestingTestteamsOverviewGrid.Strength3)
          val strength4Opt = row.inputs(PigTestingTestteamsOverviewGrid.Strength4)
          val backfat1Opt = row.inputs(PigTestingTestteamsOverviewGrid.Backfat1)
          val backfat2Opt = row.inputs(PigTestingTestteamsOverviewGrid.Backfat2)
          val backfat3Opt = row.inputs(PigTestingTestteamsOverviewGrid.Backfat3)
          val backfat4Opt = row.inputs(PigTestingTestteamsOverviewGrid.Backfat4)

          val giltId = row.metadata
          val testteamLocationId = sql"""
          select location_id from testteam where id = $testteamId
        """.map(rs => rs.long("location_id")).single.apply()

          sql"""
            update gilt set
              teststart = $testStart,
              testeduntil = $testEnd,
              teststartweight = $testStartWeight,
              testendweight = $testEndWeight,
              breed = $breed,
              testresult = $testResult,
              dg_test_result = $dgTestResult
            where id = $giltId
          """.execute.apply()

          if (
            Seq(strength1Opt, strength2Opt, strength3Opt, strength4Opt, backfat1Opt, backfat2Opt, backfat3Opt, backfat4Opt).flatten.nonEmpty
          ) {
            val hopId = sql"""
                insert into hop (gilt_id, actor_date, to_location_id) values ($giltId, NOW(), $testteamLocationId) returning id
              """.map(rs => rs.long("id")).single.apply()
            sql"""
                insert into backfat (id, backfat1, backfat2, backfat3, backfat4, strength1, strength2, strength3, strength4)
                  values ($hopId, $backfat1Opt, $backfat2Opt, $backfat3Opt, $backfat4Opt, $strength1Opt, $strength2Opt, $strength3Opt, $strength4Opt)
              """.execute.apply()
          }

          giltId
        }
      }
    }.toMap

    override def removeGilt(testteamId: Long, giltId: Long): Unit = t.require(Access.PigTesting.write).withSession.transaction { ts =>
      import ts.implicits._

      sql"""
        update gilt set testteam_id = null where id = $giltId
      """.execute.apply()

    }

    override def addTestTeam(testTeamData: Seq[RowSaveDTO[PigTestingTestteamsGrid, Option[Long]]]): Map[Int, Long Or Every[ServerProblem]] = {
      t.require(Access.PigTesting.write).apply { vt =>
        testTeamData.map { rowSaveDTO =>
          rowSaveDTO.rowId -> vt.withSession.resultOrProblems(rowSaveDTO.acceptedWarnings) { context =>
            import context.ts.implicits._
            val row = rowSaveDTO.validateOrThrow(PigTestingTestteamsGrid)(())
            case class TestTeam(id: Long, start: Timestamp, locationName: String)

            val locationId = row.inputs(PigTestingTestteamsGrid.Location)
            val testStart = row.inputs(PigTestingTestteamsGrid.TestStart)
            val testEndOpt = row.inputs(PigTestingTestteamsGrid.TestEnd)
            val repeat = row.inputs(PigTestingTestteamsGrid.Repeat)
            val year = row.inputs(PigTestingTestteamsGrid.Year)
            val comment = row.inputs(PigTestingTestteamsGrid.Comment)
            val cfStatus = row.inputs(PigTestingTestteamsGrid.Status)

            val isLocationTest = sql"select fortest from location where id = $locationId".map(rs => rs.boolean("fortest")).single.apply()
            if (!isLocationTest.getOrElse(false)) {
              context.addError(PigTestingTestteamsGrid.Location, messages("validation.error.location.fortest.notset"))
            }
            val existingTestTeamCondition = testEndOpt match {
              case Some(testEnd) => sqls"""
          (teststart between $testStart and $testEnd)
          or (testend between $testStart and $testEnd)
          or ($testStart between teststart and testend)
          or ($testEnd between teststart and testend)"""
              case None => sqls"$testStart < testend or testend is null"
            }
            val existingTestTeamOpt = {
              sql"select t.id, teststart, l.fullreversenumber from testteam t join location l on t.location_id = l.id where location_id = $locationId and ($existingTestTeamCondition) limit 1"
                .map(rs => TestTeam(rs.long("id"), rs.timestamp("teststart"), rs.string("fullreversenumber"))).single.apply()
            }

            (existingTestTeamOpt, row.metadata) match {
              case (Some(existingTestTeam), testTeamId) if testTeamId.isEmpty || testTeamId.get != existingTestTeam.id =>
                throw BusinessException(messages(
                  "validation.error.test.team.already.exists",
                  existingTestTeam.locationName,
                  existingTestTeam.start,
                ))
              case (_, Some(testTeamId)) =>
                sql"""
                  update testteam set
                    location_id = $locationId,
                    teststart = $testStart,
                    testend = $testEndOpt,
                    repeat = $repeat,
                    danavl_year = $year,
                    comment = $comment,
                    cf_status = $cfStatus
                  where
                    id = $testTeamId
                """.execute.apply()
                testTeamId
              case (_, None) =>
                sql"""
              insert into testteam
              (location_id, teststart, testend, repeat, danavl_year, comment) values
              ($locationId, $testStart, $testEndOpt, $repeat, $year, $comment) returning id
            """.map(rs => rs.long("id")).single.apply().getOrElse(-1.toLong)
            }
          }
        }
      }.toMap
    }

    override def removeTestTeam(testTeamId: Long): Unit = t.require(Access.PigTesting.write).withSession.transaction { ts =>
      import ts.implicits._

      sql"""
        delete from testteam where id = $testTeamId
      """.execute.apply()
    }

    override def addGiltsToTestteam(
      testteamId: Long,
      date: Instant,
      animalId: String,
      startWeightOpt: Option[Long],
      supressWarnings: Boolean = false,
    ): Seq[String] = t.require(Access.PigTesting.write).withSession.transaction { ts =>
      import ts.implicits._

      case class IdBreedSex(id: Long, breed: String, sex: String)
      val testteamLocationId = sql"""
        select location_id from testteam where id = $testteamId
      """.map(rs => rs.long("location_id")).single.apply()
      val giltIdBreeSexFromData = sql"""
        select id, breed, sex from gilt where animalid = $animalId
      """.map(rs => IdBreedSex(rs.long("id"), rs.string("breed"), rs.string("sex"))).single.apply()
      val isGiltActive = sql"""
        select active from gilt where animalid = $animalId
      """.map(rs => rs.booleanOpt("active")).single.apply().flatten
      val giltWeightOpt = sql"""
        select weight
        from hop h
        join gilt g on g.id = h.gilt_id
        where weight is not null and g.animalid = $animalId order by actor_date desc limit 1
      """.map(rs => rs.double("weight")).single.apply()
      val testTeamFromGilt = sql"""
        select testteam_id from gilt where animalid = $animalId
      """.map(rs => rs.longOpt("testteam_id")).single.apply()
      val testTeamBreedSex = sql"""
        select tt.id, same(g.breed) breed, same(g.sex) sex
        from testteam tt
        join gilt g on g.testteam_id = tt.id
        where tt.id = $testteamId
        group by tt.id""".map(rs => IdBreedSex(rs.long("id"), rs.string("breed"), rs.string("sex"))).single.apply()

      val breedSettingsOptMap = giltIdBreeSexFromData match {
        case None => None
        case Some(gilt) => sql"""
            select minteststartage, maxteststartage, maxtesteduntil, minstartweight, maxstartweight from breedconfig where breed = ${gilt.breed} order by update_date desc limit 1
          """.map(rs => {
            Map(
              "minTestStartAge" -> rs.double("minteststartage"),
              "maxTestStartAge" -> rs.double("maxteststartage"),
              "maxTestedUntil" -> rs.double("maxtesteduntil"),
              "minStartWeight" -> rs.double("minstartweight"),
              "maxStartWeight" -> rs.double("maxstartweight"),
            )
          }).single.apply()
      }

      def testBreedSetting(breedSettingsOptMap: Option[Map[String, Double]], key: String, testFunction: Double => Boolean) = {
        breedSettingsOptMap match {
          case None => false
          case Some(breedSettingsMap) =>
            if (!breedSettingsMap.contains(key))
              false
            else {
              testFunction(breedSettingsMap(key))
            }
        }
      }

      val giltAgeOpt = {
        sql"""
          select birthdate from gilt where animalid = $animalId
        """.map { rs =>
          val birthDate = rs.date("birthdate").toLocalDate
          ChronoUnit.DAYS.between(birthDate, TimeZoneUtil(t.user.getFarmTimezoneName).instantToLocalDate(date))
        }.single.apply()
      }

      if (giltIdBreeSexFromData.isEmpty) {
        throw BusinessException(messages("validation.error.test.team.animal.not.found", animalId))
      } else if (testteamLocationId.isEmpty) {
        throw BusinessException(messages("validation.error.test.location.not.found"))
      } else if (testTeamFromGilt.nonEmpty && testTeamFromGilt.get.nonEmpty) {
        throw BusinessException(messages("validation.error.test.in.another.testteam"))
      } else if (testTeamBreedSex.nonEmpty && testTeamBreedSex.get.breed != giltIdBreeSexFromData.get.breed) {
        throw BusinessException(messages("validation.error.test.different.breed"))
      } else if (testTeamBreedSex.nonEmpty && testTeamBreedSex.get.sex != giltIdBreeSexFromData.get.sex) {
        throw BusinessException(messages("validation.error.test.different.sex"))
      } else if (isGiltActive.nonEmpty && !isGiltActive.get) {
        throw BusinessException(messages("validation.error.test.animal.not.active"))
      }

      val warnings = Map(
        (giltAgeOpt.nonEmpty && testBreedSetting(breedSettingsOptMap, "minTestStartAge", _ > giltAgeOpt.get)) -> messages(
          "validation.error.test.animal.younger",
        ),
        (giltAgeOpt.nonEmpty && testBreedSetting(breedSettingsOptMap, "maxTestStartAge", _ < giltAgeOpt.get)) -> messages(
          "validation.error.test.animal.older",
        ),
        (giltWeightOpt.nonEmpty && testBreedSetting(breedSettingsOptMap, "minStartWeight", _ > giltWeightOpt.get)) -> messages(
          "validation.error.test.weights.less",
        ),
        (giltWeightOpt.nonEmpty && testBreedSetting(breedSettingsOptMap, "maxStartWeight", _ < giltWeightOpt.get)) -> messages(
          "validation.error.test.weights.more",
        ),
      ).view.filterKeys(x => x).values

      if (warnings.nonEmpty && !supressWarnings) {
        warnings.toSeq
      } else {
        val idOpt = sql"""
          insert into hop (gilt_id, actor_date, to_location_id) values (${giltIdBreeSexFromData.get.id}, $date, ${testteamLocationId.get}) returning id
        """.map(rs => rs.long("id")).single.apply()
        if (idOpt.isEmpty) {
          throw new Exception
        } else {
          sql"""
          update gilt set testteam_id = $testteamId, teststart = $date, teststartweight = $startWeightOpt, testresult = 2 where id = ${giltIdBreeSexFromData.get.id}
          """.execute.apply()
          Seq()
        }
      }
    }

    override def getAnimalIdsOfTestTeam(testTeamId: Long): Seq[String] = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      sql"""
      select animalid from gilt where testteam_id = $testTeamId
      """.map(_.string("animalid")).list.apply()
    }

    override def getAnimalIdsWithoutTestteam(term: String): Seq[String] = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      sql"""
      select animalid from gilt where testteam_id is null and active = true and animalid like $term || '%' limit 20
      """.map(_.string("animalid")).list.apply()
    }

    override def getAnimalIdsInTestTeams: Seq[String] = t.require(Access.PigTesting.read).withSession.readOnly { ts =>
      import ts.implicits._

      sql"""
      select animalid from gilt where testteam_id is not null
      """.map(_.string("animalid")).list.apply()
    }
  }

  def withTenant(t: AutowireContext) = new PigTestingTestteamsServiceImpl(t)
}
