package domains.batches

import common.ValidatedResult.Error
import domains.common.ScalikeOps
import domains.common.grid2.GridScalikeOps.{ ColumnGenerator, GenerateColumn }
import domains.common.grid2.{ DBModelRow, ServerContext, ServerContextBuilder }
import domains.kpi.KpiDefinitionService
import domains.kpi.batch.{ BatchIndex, BatchRawKpiProviderSource, FarmBatchId }
import domains.kpi.calculator.KpiCalculatorService
import models._
import models.handlers.SettingsHandler
import org.scalactic.{ Bad, Every, Good, Or }
import reactapp.shared.auth.Access
import reactapp.shared.batches.BatchesPageApi._
import reactapp.shared.batches._
import reactapp.shared.batches.detail._
import reactapp.shared.common.grid.CfDependency
import reactapp.shared.reports.batches.efficiency.holdingoverview.HoldingBatchesOverviewPageApi.FarmId
import scalikejdbc._
import security.ApplicationUser
import sjsgrid.shared.form.DynamicValue
import sjsgrid.shared.form.SharedValidation.CustomError
import sjsgrid.shared.grid.dto.{ FilteringAndSorting, RowModel, RowSaveDTO, ServerProblem }
import slogging.StrictLogging
import tenant.{ AccountFarmLangSession, AutowireContext, SessionProvider }
import utils.CalendarUtilsJTime
import utils.api.TenantMessages
import utils.avgweight.AverageWeightCalculator.{ LocationId => AvgWeightLocId }
import utils.avgweight.{ AvgWeightCalculator, AvgWeightCalculatorSplitter }

import java.sql.Connection
import java.time.temporal.ChronoUnit
import java.time.{ Duration, Instant, LocalDate, ZoneId }
import java.util.{ Collections, Date }
import javax.inject.{ Inject, Singleton }
import scala.math.BigDecimal.RoundingMode

/**
  * Created by Milan Satala
  * Date: 12/28/16
  * Time: 9:19 AM
  */
@Singleton
class BatchesPageService @Inject() (
  messages: TenantMessages,
  kpiCalculatorService: KpiCalculatorService,
  kpiDefService: KpiDefinitionService,
  calendarUtilsJTime: CalendarUtilsJTime,
  scalikeOps: ScalikeOps,
  batchesPageRepository: BatchesPageRepository,
)(implicit sessionProvider: SessionProvider, serverContextBuilder: ServerContextBuilder, settingsHandler: SettingsHandler)
    extends StrictLogging {
  import scalikeOps._
  import batchesPageRepository._

  private val batchesPageGrid: BatchesGrid = BatchesGrid()

  implicit val columnGenerator: ColumnGenerator[BatchesGridColumn] = _ => GenerateColumn.DefinedInGridOrSql

  def commonBatchProps(batchId: Long)(implicit con: Connection): BatchValidationProps = {
    import quill.QuillContext._

    run(
      for {
        batch <- query[qfarm.Location]
        if batch.id == lift(batchId)
      } yield BatchValidationProps(batch.validfrom, batch.validto, batch.inhabitanttypeCode, lift(false)),
    ).execute()
      .head
  }

  class TenantWrapper(t: AutowireContext) extends BatchesPageApi {

    import BatchesPageApi._

    def list(): Seq[BatchDbRow] = t.require(Access.Batches.read).withSession.readOnly { ts =>
      import ts.implicits._

      listSql(FilteringAndSorting.empty)
        .map(rs => {
          BatchDbRow(
            batchId = rs.long("id"),
            updateTag = rs.string("update_date"),
            rowModel = DBModelRow.allColumns(batchesPageGrid)(rs),
          )
        })
        .list
        .apply()
    }

    def selectWithoutFilteringAndSorting(batchCteSqls: SQLSyntax, joinsSql: SQLSyntax, farmIdOpt: Option[Long] = None)(implicit
      serverContext: ServerContext,
      user: ApplicationUser,
    ): SQLSyntax = {
      val farmPrefix = farmIdPrefix(farmIdOpt)
      val farmCtePostfix = farmIdCtePostfix(farmIdOpt)
      sqls"""$batchCteSqls
                  SELECT
                    batch.id,
                    batch.update_date,
                    coalesce(trins$farmCtePostfix.sum, 0)                                           AS IN,
                    coalesce(trouts$farmCtePostfix.sum, 0)                                          AS OUT,
                    coalesce(buys$farmCtePostfix.sum, 0)                                            AS Bought,
                    coalesce(sells$farmCtePostfix.sum, 0)                                           AS Sold,
                    coalesce(weanings$farmCtePostfix.sum, 0)                                        AS Weaned,
                    coalesce(deaths$farmCtePostfix.sum, 0)                                          AS Dead,
                    coalesce(feeds$farmCtePostfix.sum, 0)                                           AS SumFeed,
                    coalesce(buys$farmCtePostfix.sum, 0) + coalesce(trins$farmCtePostfix.sum, 0) + coalesce(weanings$farmCtePostfix.sum, 0) - coalesce(sells$farmCtePostfix.sum, 0) - coalesce(trouts$farmCtePostfix.sum, 0) - coalesce(deaths$farmCtePostfix.sum, 0) AS Difference,
                    coalesce(corr$farmCtePostfix.abs, 0)                                            AS CorrectionsAbsolute,
                    coalesce(corr$farmCtePostfix.sum, 0)                                            AS CorrectionsTotal,
                    coalesce(buys$farmCtePostfix.sum, 0) + coalesce(trins$farmCtePostfix.sum, 0) + coalesce(weanings$farmCtePostfix.sum, 0) - coalesce(sells$farmCtePostfix.sum, 0) - coalesce(trouts$farmCtePostfix.sum, 0) - coalesce(deaths$farmCtePostfix.sum, 0) + coalesce(corr$farmCtePostfix.sum, 0) AS EndState,
                    ${daysInBatchSelectSqls(farmIdOpt)} as DaysInBatch,
                    ${if (farmIdOpt.isDefined) sqls"${SQLSyntax.createUnsafely(s"${farmIdOpt.get} AS farmId,")}"
      else SQLSyntax.createUnsafely("null AS farmId,")}
                    ${BatchesGrid(farmIdOpt.isDefined).allColumnsSql}
           FROM ${farmPrefix}location AS batch
                    JOIN ${farmPrefix}location AS current_location ON current_location.id = (SELECT location_id
                                                                                     FROM ${farmPrefix}batch_location
                                                                                     WHERE batch_id = batch.id
                                                                                     ORDER BY entry_date DESC NULLS LAST
                                                                                     LIMIT 1)
                    LEFT OUTER JOIN trins$farmCtePostfix ON trins$farmCtePostfix.tolocation_id = batch.id
                    LEFT OUTER JOIN trouts$farmCtePostfix ON trouts$farmCtePostfix.fromlocation_id = batch.id
                    LEFT OUTER JOIN buys$farmCtePostfix ON buys$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN sells$farmCtePostfix ON sells$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN weanings$farmCtePostfix ON weanings$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN deaths$farmCtePostfix ON deaths$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN feeds$farmCtePostfix ON feeds$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN corr$farmCtePostfix ON corr$farmCtePostfix.location_id = batch.id
                    LEFT OUTER JOIN batch_closeout bco ON bco.location_id = batch.id
                    LEFT OUTER JOIN organization ON organization.id = ${farmIdOpt.map(id =>
        SQLSyntax.createUnsafely(id.toString),
      ).getOrElse(SQLSyntax.createUnsafely(t.user.getFarmId.toString))}
                    $joinsSql
        WHERE batch.locationtype_id = 'BA'
      """

    }

    def listSql(filteringAndSorting: FilteringAndSorting[BatchesGridColumn])(implicit
      user: ApplicationUser,
      serverContext: ServerContext,
    ): SQL[Nothing, NoExtractor] = {
      sql"""
        ${selectWithoutFilteringAndSorting(batchCTE(), filteringAndSorting.joinsSqlNew)}
        ${andAll(filteringAndSorting.filters.toSqlsNew)}
        ${filteringAndSorting.sortings.toSqlNew(sqls"batch.id")}
      """
    }

    /** Get a batch using an existing transaction */
    def getSingleBatch(batchId: Long)(implicit
      user: ApplicationUser,
      dbSession: DBSession,
      serverContext: ServerContext,
    ): Option[RowModel[BatchesGridColumn]] = {
      sql"""
          ${selectWithoutFilteringAndSorting(batchCTE(), sqls.empty)}
          AND batch.id = $batchId
        """
        .map(DBModelRow.allColumns(batchesPageGrid))
        .headOption.apply()
    }

    private def getExistingBatches()(implicit dbSession: DBSession): Map[Long, String] = {
      sql"""
            ${batchesPageRepository.existingBatches()}
      """.map(rs => rs.long("id") -> rs.string("name")).list().toMap
    }

    private def saveBatchCloseoutFields(batchId: Long, originSowFarmOpt: Option[String], sourceFarmOpt: Option[String])(implicit
      dbSession: DBSession,
    ) = {
      sql"""
             |insert into batch_closeout
             |  (location_id, origin_sow_farm, source_farm)
             |values
             |  ($batchId, $originSowFarmOpt, $sourceFarmOpt)
             |on conflict (location_id) do update set
             |  origin_sow_farm = excluded.origin_sow_farm,
             |  source_farm = excluded.source_farm
             |"""
        .stripMargin
        .execute.apply()
    }

    def saveBatches(batchesToSave: Seq[RowSaveDTO[BatchesGridColumn, BatchesPageApi.BatchSaveDataIn]])
      : Map[RowId, BatchSaveDataOut Or Every[ServerProblem]] = t.require(Access.Batches.write).apply { vt =>
      batchesToSave.map { row =>
        row.rowId -> vt.withSession.resultOrProblems(row.acceptedWarnings) { context =>
          import context.ts.implicits._

          row.inputsDTO.validate(batchesPageGrid)(batchesPageGrid.ValidationProps(
            row.metadata.batchIdOpt,
            getExistingBatches(),
          )).resultOrErrors match {
            case Good(batchInputs) =>
              def saveBatch(batch: Location, isAfterJournalRefresh: Boolean = false) = {
                batch.setName(batchInputs(BatchesGridColumn.Name))
                batch.setLocationNumber(batchInputs(BatchesGridColumn.Name))
                batch.setGrowthRate(batchInputs.findEntity(BatchesGridColumn.GrowthRate, classOf[GrowthRate]))
                batch.setValidFrom(instantToDate(batchInputs(BatchesGridColumn.ValidFrom)))
                batch.setComment(batchInputs(BatchesGridColumn.Comment).orNull)
                batch.setCommonExitWeight(batchInputs(BatchesGridColumn.TargetWeight).orNullJava)
                batch.setBatchTargetDate {
                  val ld = batchInputs(BatchesGridColumn.TargetDate).orNull
                  if (ld == null) null else java.sql.Date.valueOf(ld)
                }

                val batchLocations = row.metadata.batchLocations.zipWithIndex.map {
                  case (batchLocationDTO, locationIndex) =>
                    val locationInputs = batchLocationDTO.validateOrThrow(BatchLocationsGrid)(
                      BatchLocationsGrid.ValidationProps(
                        BatchValidationProps(
                          batchValidFromOpt = Some(batchInputs(BatchesGridColumn.ValidFrom)),
                          batchEndOpt = batchInputs(BatchesGridColumn.End).getAvailableOrElse(Option(batch.getValidTo).map(_.toInstant)),
                          batchInhabitantOpt = Some(batchInputs(BatchesGridColumn.Location) match {
                            case DynamicValue.Available(v) => serverContext.dependency(CfDependency.Locations).inhabitantType(v)
                            case DynamicValue.Unavailable  => batch.getInhabitanttype_code
                          }),
                        ),
                        isInitialLocation = locationIndex == 0,
                      ),
                    ).inputs

                    val batchLocation = findOrCreateEntity(batchLocationDTO.metadata, classOf[BatchLocation])(new BatchLocation())
                    batchLocation.setBatch(batch)
                    locationInputs(BatchLocationsGrid.EntryDate).foreach(v => batchLocation.setEntryDate(Date.from(v)))
                    val location = locationInputs.findEntity(BatchLocationsGrid.Location, classOf[Location])

                    if (location.getInhabitanttype_code != batch.getInhabitanttype_code)
                      throw new RuntimeException("Locations assigned to batch must have the same inhabitant type as batch itself")

                    batchLocation.setLocation(location)

                    context.em.persist(batchLocation)

                    batchLocationDTO.rowId -> batchLocation.getId
                }.toMap

                // Fields concerning the batch closeout report are saved to a separate table
                val originSowFarmOpt = batchInputs(BatchesGridColumn.OriginSowFarm)
                val sourceFarmOpt = batchInputs(BatchesGridColumn.SourceFarm)
                if (originSowFarmOpt.isDefined || sourceFarmOpt.isDefined)
                  saveBatchCloseoutFields(batch.getId, originSowFarmOpt, sourceFarmOpt)

                // Journal refresh may have affected batch columns that were not explicitly modified. Fetching them to refresh the grid
                val otherBatchColumnsToRefreshOpt = {
                  if (isAfterJournalRefresh) {
                    context.em.flush()
                    getSingleBatch(batch.getId).map(updatedBatch => {
                      OtherBatchColumnsToRefresh(
                        updatedBatch.getValue(BatchesGridColumn.EndState),
                        updatedBatch.getValue(BatchesGridColumn.CorrectionsTotal),
                        updatedBatch.getValue(BatchesGridColumn.CorrectionsAbsolute),
                      )
                    })
                  } else None
                }

                AdditionalSaveDataOut(
                  batchLocations = batchLocations,
                  otherBatchColumnsToRefresh = otherBatchColumnsToRefreshOpt,
                )
              }

              row.metadata.batchIdOpt match {
                case Some(batchId) =>
                  val batch = findEntity(batchId, classOf[Location])
                  val wasBatchOpen = batch.getValidTo == null

                  val emptyLocationOpt =
                    executeNamedQuery("empty_location.by.loc", classOf[EmptyLocation], "locationId" -> batchId).headOption

                  batchInputs(BatchesGridColumn.End).getOrThrow("End must be available when updating") match {
                    case Some(endTime) =>
                      val eventsAfterEndTime = getBatchEventsAfterEndTime(endTime, batchId)
                      if (eventsAfterEndTime.nonEmpty) {
                        val eventGridNames = eventsAfterEndTime
                          .groupBy(_.gridLabelKey)
                          .iterator
                          .map {
                            case (gridLabelKey, events) =>
                              val latest = events.map(_.instant).max
                              // TODO format instant properly
                              s"${messages(gridLabelKey)} ($latest)"
                          }
                          .mkString(", ")

                        context.addWarning(
                          BatchesGridColumn.End,
                          "error.batches.endtoosoon",
                          messages("error.batches.endtoosoon", eventGridNames),
                        )
                      }

                      val emptyLocation = emptyLocationOpt.getOrElse {
                        val newEntity = new EmptyLocation()
                        logger.debug("End time set. Creating empty location event.")

                        newEntity.setLocationId(batchId)
                        newEntity.setComment("GENERATED BY BATCH END")
                        newEntity
                      }

                      val endDate = Date.from(endTime)

                      if (!endDate.equals(emptyLocation.getActorDate)) {
                        emptyLocation.setActorId(t.user.getAccountId)
                        emptyLocation.setActorDate(endDate)
                        batch.setValidTo(endDate)
                        logger.debug("End time changed. Updating empty location event")
                        context.em.persist(emptyLocation)
                      }

                    case None => emptyLocationOpt.foreach { emptyLocation =>
                        logger.debug(s"Removing empty location event for batch ${batch.getName}")
                        batch.setValidTo(null)
                        context.em.remove(emptyLocation)
                      }
                  }

                  // If the batch was closed/reopened, force journal refresh to update the affected batch columns
                  val isBatchOpenDyn = batchInputs(BatchesGridColumn.End).map(_.isEmpty)
                  val requiresJournalRefresh = isBatchOpenDyn.exists(_ != wasBatchOpen)
                  if (requiresJournalRefresh) {
                    logger.debug(s"Batch was ${if (wasBatchOpen) "closed" else "reopened"}. Refreshing the journal")
                    context.em.createNativeQuery("select refresh_journal()").getResultList
                  }

                  BatchSaveDataOut(
                    newBatchOpt = None,
                    saveDataOut = saveBatch(batch, requiresJournalRefresh),
                  )

                case None =>
                  val batch = new Location()

                  batch.setLocationNumber(batchInputs(BatchesGridColumn.Name).replaceAll(" ", ""))
                  batch.setLocationType(context.em.find(classOf[LocationType], "BA"))

                  val initialLocation = batchInputs.findEntityOpt(BatchesGridColumn.Location, classOf[Location])
                    .getOrElse(sys.error("Initial location must be specified"))

                  batch.setInhabitanttype_code(initialLocation.getInhabitanttype_code)

                  val saveDataOut = saveBatch(batch)

                  context.em.persist(batch)

                  val batchLocation = new BatchLocation()
                  batchLocation.setBatch(batch)
                  batchLocation.setLocation(initialLocation)

                  context.em.persist(batchLocation)

                  batch.getId

                  BatchSaveDataOut(
                    newBatchOpt = Some(NewBatchData(batch.getId, batchLocation.getId)),
                    saveDataOut = saveDataOut,
                  )
              }

            case Bad(error) =>
              error.map {
                case Error.Checked(checkedError) =>
                  checkedError match {
                    case CustomError(fieldOpt, msg) => fieldOpt match {
                        case Some(value) => context.addError(value, msg)
                        case None        => context.addError(msg)
                      }
                    case _ => context.addError(checkedError.systemMessage) // No need to specifically handle other cases because these should be reflected on frontend immediately.
                  }
                case Error.Unchecked(value)       => context.addError(value.systemMessage)
                case Error.FailedRequirement(msg) => context.addError(msg)
              }
              BatchSaveDataOut(None, AdditionalSaveDataOut(Map.empty))
          }
        }
      }.toMap
    }

    def listBatchLocations(batchId: BatchId): Seq[(BatchLocationId, BatchLocationsGrid.Row)] = {
      t.require(Access.Batches.read).withSession.readOnly { ts =>
        import ts.implicits._

        val initialEntryDateOpt = getBatchEntryDate(batchId)

        sql"select id, ${BatchLocationsGrid.allColumnsSql} from batch_location where batch_id = ${batchId} order by entry_date nulls first"
          .map { rs =>
            val row = DBModelRow.allColumns(BatchLocationsGrid)(rs)

            rs.long("id") -> (if (rs.cursor.position > 1) row
                              else row.setValue(BatchLocationsGrid.EntryDate, initialEntryDateOpt))
          }
          .list
          .apply()
      }
    }

    override def getGraphData(locId: Long): Seq[BatchesGraphData] = t.require(Access.Batches.read).withSession.readOnly { ts =>
      getGraphDataInternal(locId)(ts)
    }

    private def batchEventsCTEs(batchId: BatchId): SQLSyntax = {
      sqls"""
             with buys as (
             select
               cast('b' as text)        as EventType,
               transferin.actor_date    as ActorDate,
               pigqualityin.pigamount   as Pigamount,
               pigqualityin.pigliveweight   as Pigliveweight,
               pigqualityin.age as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               pigqualityin.location_id as location_id
             from transferin
               left outer join pigqualityin on transferin.id = pigqualityin.transferin_id
             where location_id = $batchId
             union all
             select
               cast('b' as text)         as EventType,
               t.actor_date              as ActorDate,
               1                         as Pigamount,
               ti.pigliveweight       as Pigliveweight,
               (t.actor_date at time zone o.timezonename) :: date - coalesce(sow.birthdate,boar.birthdate,gilt.birthdate) as Age,
               coalesce(sow.sownumber,boar.boarnumber,gilt.sownumber) as AnimalName,
               coalesce(sow.animalid,boar.animalid,gilt.animalid) as AnimalId,
               ti.location_id         as location_id
             from organization o cross join transferin t
               left outer join transferindividualin ti on t.id = ti.transferin_id
               left outer join sow on sow.id = ti.sow_id
               left outer join boar on boar.id = ti.boar_id
               left outer join gilt on gilt.id = ti.gilt_id
             where ti.location_id = $batchId and o.id = get_tenant_organization()
         ), sells as (
             select
               cast('s' as text)         as EventType,
               transferout.actor_date    as ActorDate,
               pigqualityout.pigamount   as Pigamount,
               pigqualityout.pigliveweight   as Pigliveweight,
               pigqualityout.age   as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               pigqualityout.location_id as location_id
             from transferout
               left outer join pigqualityout on transferout.id = pigqualityout.transferout_id
             where location_id = $batchId
             union all
             select
               cast('s' as text)         as EventType,
               t.actor_date              as ActorDate,
               1                         as Pigamount,
               tiout.pigliveweight       as Pigliveweight,
               (t.actor_date at time zone o.timezonename) :: date - coalesce(sow.birthdate,boar.birthdate,gilt.birthdate) as Age,
               coalesce(sow.sownumber,boar.boarnumber,gilt.sownumber) as AnimalName,
               coalesce(sow.animalid,boar.animalid,gilt.animalid) as AnimalId,
               tiout.location_id         as location_id
             from organization o cross join transferout t
               left outer join transferindividualout tiout on t.id = tiout.transferout_id
               left outer join sow on sow.id = tiout.sow_id
               left outer join boar on boar.id = tiout.boar_id
               left outer join gilt on gilt.id = tiout.gilt_id
             where tiout.location_id = $batchId and o.id = get_tenant_organization()
         ), ins as (
             select
               cast('i' as text)           as EventType,
               transferloc.actor_date      as ActorDate,
               pigqualityloc.pigamount     as Pigamount,
               pigqualityloc.pigliveweight     as Pigliveweight,
               pigqualityloc.age     as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               pigqualityloc.tolocation_id as location_id
             from transferloc
               left outer join pigqualityloc on transferloc.id = pigqualityloc.transferloc_id
             where tolocation_id = $batchId
         ), outs as (
             select
               cast('o' as text)             as EventType,
               transferloc.actor_date        as ActorDate,
               pigqualityloc.pigamount       as Pigamount,
               pigqualityloc.pigliveweight       as Pigliveweight,
               pigqualityloc.age       as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               pigqualityloc.fromlocation_id as location_id
             from transferloc
               left outer join pigqualityloc on transferloc.id = pigqualityloc.transferloc_id
             where fromlocation_id = $batchId
         ), deaths as (
             select
               cast('d' as text) as EventType,
               d.actor_date as ActorDate,
               d.amount as Pigamount,
               d.weight as Pigliveweight,
               coalesce((d.actor_date at time zone o.timezonename) :: date - coalesce(sow.birthdate,boar.birthdate,gilt.birthdate), d.age) as Age,
               coalesce(sow.sownumber,boar.boarnumber,gilt.sownumber) as AnimalName,
               coalesce(sow.animalid,boar.animalid,gilt.animalid) as AnimalId,
               d.location_id
             from organization o
               cross join dead d
               left join sow on sow.id = d.sow_id
               left join boar on boar.id = d.boar_id
               left join gilt on gilt.id = d.gilt_id
             where d.serving_id is null
               and d.deathtype_code is not null
               and d.location_id = $batchId
               and o.id = get_tenant_organization()
         ), corr as (
             select
               cast('c' as text) as EventType,
               actor_date as ActorDate,
               amount as Pigamount,
               null::int as Pigliveweight,
               null::int as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               location_id
             from journal
               where location_id = $batchId and stocktaking = 1 and amount <> 0
         ), weans as (
             select
               cast('w' as text)           as EventType,
               weaned.actor_date      as ActorDate,
               weaned.amount     as Pigamount,
               weaned.weight     as Pigliveweight,
               null::int     as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               weaned.location_id as location_id
             from weaned
             where location_id = $batchId
         ), feedcon as (
             select
               cast('fc' as text)           as EventType,
               fc.actor_date      as ActorDate,
               null::int     as Pigamount,
               null::int     as Pigliveweight,
               null::int     as Age,
               null::text as AnimalName,
               null::text as AnimalId,
               fc.location_id as location_id
             from feedconsumption fc
             where location_id = $batchId
         ), medusage as (
             select
               cast('mu' as text)           as EventType,
               mu.actor_date      as ActorDate,
               null::int     as Pigamount,
               null::int     as Pigliveweight,
               (mu.actor_date at time zone o.timezonename) :: date - coalesce(sow.birthdate,boar.birthdate,gilt.birthdate) as Age,
               coalesce(sow.sownumber,boar.boarnumber,gilt.sownumber) as AnimalName,
               coalesce(sow.animalid,boar.animalid,gilt.animalid) as AnimalId,
               mu.location_id as location_id
             from organization o cross join medicine_usage mu
             left join sow on sow.id = mu.sow_id
             left join boar on boar.id = mu.boar_id
             left join gilt on gilt.id = mu.gilt_id
             where mu.location_id = $batchId and o.id = get_tenant_organization()
         )
        """
    }

    override def checkForEventsBeforeBatchDelete(batchId: BatchId): Map[String, Int] = {
      t.require(Access.Batches.read).withSession.readOnly { ts =>
        import ts.implicits._

        val events = Seq(sqls"buys", sqls"sells", sqls"ins", sqls"outs", sqls"deaths", sqls"weans", sqls"feedcon", sqls"medusage")
        def selectFrom(cteTable: SQLSyntax) = sqls"select EventType, count(1) as counted from $cteTable group by EventType"
        val unionAllSyntax = events.map(selectFrom).reduceLeft((current, next) => sqls"""|$current
                                                                                       |UNION ALL
                                                                                       |$next""".stripMargin)

        sql"""
         ${batchEventsCTEs(batchId)}
         $unionAllSyntax
         """.map(rs => rs.string("EventType") -> rs.int("counted")).list.apply().toMap
      }
    }

    override def getBatchEvents(batchId: BatchId): Seq[BatchEventGrid.Row] = t.require(Access.Batches.read).withSession.readOnly { ts =>
      import ts.implicits._

      def selectFrom(cteTableName: SQLSyntax) =
        sqls"""SELECT EventType, ActorDate, Pigamount, Pigliveweight, Age, AnimalName, AnimalId, location_id FROM $cteTableName"""
      val unionSyntax = {
        val batchEventsGrids = Seq(LocalTransfersGrid, BuyingGrid, SellingGrid, DeadGrid, WeaningGrid)
        val syntaxSeq = batchEventsGrids.collect {
          case LocalTransfersGrid if Access.BatchesSection.LocalTransfersSection.read.isAuthorized =>
            sqls"""|${selectFrom(sqls"ins")}
                   |UNION ALL
                   |${selectFrom(sqls"outs")}""".stripMargin
          case BuyingGrid if Access.BatchesSection.BuyingSection.read.isAuthorized   => selectFrom(sqls"buys")
          case SellingGrid if Access.BatchesSection.SellingSection.read.isAuthorized => selectFrom(sqls"sells")
          case DeadGrid if Access.BatchesSection.DeadSection.read.isAuthorized       => selectFrom(sqls"deaths")
          case WeaningGrid if Access.BatchesSection.WeaningSection.read.isAuthorized => selectFrom(sqls"weans")
        } :+ selectFrom(sqls"corr")
        if (syntaxSeq.isEmpty)
          sqls""
        else {
          syntaxSeq.reduceLeft((current, next) => sqls"""|$current
                                                         |UNION ALL
                                                         |$next""".stripMargin)
        }
      }
      if (unionSyntax.isEmpty)
        List.empty
      else {
        sql"""
         ${batchEventsCTEs(batchId)}
         ${selectFrom(
          sqls"""(
            $unionSyntax
            ) as unions""",
        )}
          """
          .map(DBModelRow.allColumns(BatchEventGrid))
          .list
          .apply()
      }
    }

    override def calculateKpis(batchId: BatchId) = t.require(Access.Batches.read).withSession.readOnly { ts =>
      calculateKpisInternal(t.user.getFarmId, batchId)(ts)
    }

    def deleteLocation(locationId: Long): scala.Unit = t.require(Access.Batches.write).withSession.transaction { ts =>
      import ts.implicits._

      ts.em.remove(findEntity(locationId, classOf[BatchLocation]))
    }

    def deleteBatch(batchId: BatchId, updateTag: String): scala.Unit = t.require(Access.Batches.write).withSession.transaction { ts =>
      import ts.implicits._

//      val batch = findEntity(batchId, classOf[Location])
//      executeNamedQuery("BatchLocation.for.batch", classOf[BatchLocation], "batch" -> batch).foreach(em.remove)
//      em.remove(batch)
      sql"""delete from empty_location where location_id = $batchId and $updateTag::timestamptz = (select update_date from location where location.id = $batchId)""".execute.apply()
      sql"""delete from journal where location_id = $batchId and $updateTag::timestamptz = (select update_date from location where location.id = $batchId)""".execute.apply()
      sql"""delete from location where id = $batchId and $updateTag::timestamptz = update_date""".execute.apply()
    }

    def getBatchEventsAfterEndTime(endTime: Instant, batchId: BatchId)(implicit dbSession: DBSession): Seq[LastEvent] = {

      sql"""
        select *
        from (
       (select
          ${LocalTransfersGrid.labelKey} as EventType,
          actor_date        as ActorDate
        from transferloc
          left outer join pigqualityloc on transferloc.id = pigqualityloc.transferloc_id
        where tolocation_id = $batchId and actor_date > $endTime
        )
       union all
       (select
          ${LocalTransfersGrid.labelKey}      as EventType,
          transferloc.actor_date as ActorDate
        from transferloc
          left outer join pigqualityloc on transferloc.id = pigqualityloc.transferloc_id
        where fromlocation_id = $batchId and actor_date > $endTime
        )
       union all
       (select
          ${BuyingGrid.labelKey}     as EventType,
          transferin.actor_date as ActorDate
        from transferin
          left outer join pigqualityin on transferin.id = pigqualityin.transferin_id
        where location_id = $batchId and actor_date > $endTime
        )
       union all
       (select
          'js.title.batches.stocktaking'      as EventType,
          transferout.actor_date as ActorDate
        from transferout
          left outer join pigqualityout on transferout.id = pigqualityout.transferout_id
        where location_id = $batchId and actor_date > $endTime
        )
       union all
       (select
           ${DeadGrid.labelKey} as EventType,
           actor_date        as ActorDate
         from
           dead d
         where d.sow_id is null
               and d.boar_id is null
               and d.gilt_id is null
               and d.serving_id is null
               and actor_date > $endTime
               and location_id = $batchId)
        union all
       (select
           ${FeedConsumptionGrid.labelKey}                as EventType,
           coalesce(measuredate,actor_date) as ActorDate
         from
           feedconsumption
         where location_id = $batchId and coalesce(measuredate,actor_date) > $endTime
       )
      union all
       (select
          ${BatchesWeightGroupsGrid.labelKey} as EventType,
          actor_date        as ActorDate
        from
          weightgroup
        where location_id = $batchId and actor_date > $endTime
        )
      union all
      (select
          ${WeaningGrid.labelKey} as EventType,
          actor_date        as ActorDate
        from
          weaned
        where location_id = $batchId and actor_date > $endTime
        )
     ) as unions
      """
        .map(rs => LastEvent(rs.string("EventType"), rs.instant("ActorDate"))).list.apply()
    }

    def getBatchEntryDate(batchId: BatchId)(implicit session: DBSession): Option[Instant] = {
      sql"""
      select *
      from
        ((select
            actor_date        as ActorDate
          from transferloc
            left outer join pigqualityloc on transferloc.id = pigqualityloc.transferloc_id
          where tolocation_id = $batchId
          order by actor_date asc
          limit 1
      )
      union all
          (select
            transferin.actor_date as ActorDate
          from transferin
            left outer join pigqualityin on transferin.id = pigqualityin.transferin_id
          where location_id = $batchId
          order by actor_date asc
          limit 1
      )) as unions
      order by ActorDate asc
      limit 1;
      """.map(rs => rs.instantOpt("ActorDate")).single.apply().flatten
    }

    override def getAverageExitAgeOpt(batchIds: Seq[BatchId], exitDate: Instant): Seq[(BatchId, Int)] = {
      t.require(Access.Batches.read).withSession.readOnly { ts =>
        import ts.implicits._

        val entryAnimals = listEntryAnimalsInternal(batchIds)

        computeAverageExitAgeInternalOpt(batchIds, exitDate.toEpochMilli, entryAnimals)
          .collect { case (batchId, avgExitAgeOpt) if avgExitAgeOpt.isDefined => batchId -> avgExitAgeOpt.get }
      }
    }
  }

  def getGraphDataInternal(locId: Long)(ts: AccountFarmLangSession): Seq[BatchesGraphData] = { // ts needed for AvgWeightCalculator
    import ts.implicits._

    val batchIdOpt = sql"select id, inhabitanttype_code from location where id = $locId".map(r =>
      new AvgWeightLocId(r.long(1), r.string(2)),
    ).single.apply()

    case class GrowthRatePart(ageFrom: Int, ageTo: Int, weightFrom: Double, weightTo: Double)
    def loadGrowthRateData = {
      sql"""select g.agefrom, g.ageto, g.weightfrom, g.weightto
               from growthratedata g join location l on l.growthrate_id = g.growthrate_id
               where l.id = $locId order by g.agefrom""".map(rs =>
        GrowthRatePart(rs.int("agefrom"), rs.int("ageto"), rs.double("weightfrom"), rs.double("weightto")),
      ).list.apply()
    }

    batchIdOpt match {
      case None => Seq.empty
      case Some(batchId) =>
        val zoneId = ZoneId.of(ts.user.getFarmTimezoneName)
        val eventDates: List[LocalDate] = {
          sql"""
               |with locval as (
               |    select
               |      (coalesce(l.validfrom, now()) at time zone o.timezonename) :: date validfrom,
               |      (coalesce(l.validto, now()) at time zone o.timezonename) :: date   validto
               |    from
               |      organization o cross join
               |      location l
               |    where l.id = $locId and o.id = get_tenant_organization()
               |)
               |select distinct actor_date from journal j join location l on j.location_id = l.id where l.id = $locId
               |  union select validfrom from locval
               |  union select validto from locval
               | order by actor_date
        """.stripMargin.map(rs => rs.date("actor_date").toLocalDate).list.apply()
        }

        val minDate = eventDates.head
        val maxDate = eventDates.last
        val secondWeekStart = calendarUtilsJTime.farmWeekStart(ts.user, minDate.plusDays(7))
        val lastWeekStart = calendarUtilsJTime.farmWeekStart(ts.user, maxDate)
        val weeksBetween = (ChronoUnit.DAYS.between(secondWeekStart, lastWeekStart) / 7).toInt

        val addedDates = (for (i <- 0 to weeksBetween) yield secondWeekStart.plusWeeks(i)) ++ Seq(maxDate.minusDays(1))

        val dates: List[LocalDate] = (eventDates ++ addedDates).distinct.sortWith(_ isBefore _)

        import scala.jdk.CollectionConverters._
        val calc: AvgWeightCalculator = new AvgWeightCalculatorSplitter(
          Collections.singletonList(batchId),
          dates.map(d => d.atTime(23, 59, 59)).asJava,
          user,
          connection,
          settingsHandler,
        ).getCalculatorBasedOnSetting()
        val datesWeightsAndAmounts = dates.zipWithIndex.map {
          case (date, ix) =>
            (date, calc.getLocationAverageWeight(ix + 1, batchId))
        }

        def getMaybeAgeAfterOneWeek(growthCurve: List[GrowthRatePart], avgWeightAfterWeek: Double): Option[Int] = {
          growthCurve collectFirst {
            case GrowthRatePart(ageFrom, ageTo, weightFrom, weightTo)
                if avgWeightAfterWeek >= weightFrom && avgWeightAfterWeek <= weightTo =>
              (ageFrom + (ageTo - ageFrom) * (avgWeightAfterWeek - weightFrom) / (weightTo - weightFrom)).round.intValue
          }
        }

        val maybeIdealCurve: Option[(LocalDate, Int, List[GrowthRatePart])] = {
          for {
            (firtsDateWithAnimals, _) <- datesWeightsAndAmounts.find(_._2.amount > 0)
            weekAfterFirst = firtsDateWithAnimals.plusDays(7)
            calc1 = new AvgWeightCalculatorSplitter(
              Collections.singletonList(batchId),
              Collections.singletonList(weekAfterFirst.atStartOfDay()),
              user,
              connection,
              settingsHandler,
            ).getCalculatorBasedOnSetting()
            avgsAfterOneWeek <- Option(calc1.getLocationAverageWeight(1, batchId))
            avgWeightAfterWeek = avgsAfterOneWeek.avgWeight
            if avgWeightAfterWeek > 0.0
            growthCurve = loadGrowthRateData
            ageAfterOneWeek <- getMaybeAgeAfterOneWeek(growthCurve, avgWeightAfterWeek)
          } yield (weekAfterFirst, ageAfterOneWeek, growthCurve)
        }

        val dateToIdealWeight: (LocalDate, Double) => Option[Double] = maybeIdealCurve match {
          case Some((startDate, startAge, idealCurve)) =>
            (d: LocalDate, realAvgWeight: Double) => {
              if (realAvgWeight <= 0) None
              else {
                val age = ChronoUnit.DAYS.between(startDate, d).intValue + startAge
                val computedWeight: Option[Double] = idealCurve collectFirst {
                  case GrowthRatePart(ageFrom, ageTo, weightFrom, weightTo) if age >= ageFrom && age <= ageTo =>
                    weightFrom + (weightTo - weightFrom) * (age - ageFrom) / (ageTo - ageFrom)
                }
                computedWeight
              }
            }
          case None => (_, _) => None
        }

        datesWeightsAndAmounts map {
          case (date, data) =>
            BatchesGraphData(
              Option(data.amount),
              Option(data.totalWeight),
              Some(data.avgWeight),
              optimalWeight = dateToIdealWeight(date, data.avgWeight),
              date.atStartOfDay(zoneId).toInstant.toEpochMilli,
            )
        }
    }
  }

  // todo psuchan tenant refactoring kpi calculator tenant session -> user? dbSession?
  def calculateKpisInternal(farmId: FarmId, batchId: BatchId)(ts: AccountFarmLangSession): BatchKpis = {
    import ts.implicits._
    val kpiCalculator = new kpiCalculatorService.KpiCalculator(
      new BatchRawKpiProviderSource(Seq(BatchIndex(1, FarmBatchId(farmId, batchId))), None),
      Map.empty,
    )
    val kpis = kpiDefService.getStaticKpiDefinitionsMap

    BatchKpis(
      MOVEMENT_KPIS.map {
        case (key, kpi) =>
          BatchMovementKpi(
            key,
            kpiCalculator.calculate(kpis(kpi + "_AMOUNT"), 1).map(_.toInt),
            kpiCalculator.calculate(kpis(kpi + "_TOTAL_WEIGHT"), 1),
            kpiCalculator.calculate(kpis(kpi + "_AVG_WEIGHT"), 1),
            kpis.get(kpi + "_AVG_AGE").flatMap(kpi => kpiCalculator.calculate(kpi, 1)).map(_.toInt),
          )
      }.toSeq,
      FEED_KPIS.map {
        case (key, kpi) =>
          BatchFeedKpi(
            key,
            kpis(kpi + "_WEIGHT").decimalPlaces,
            kpiCalculator.calculate(kpis(kpi + "_WEIGHT"), 1),
            kpis(kpi + "_ENERGY").decimalPlaces,
            kpiCalculator.calculate(kpis(kpi + "_ENERGY"), 1),
          )
      }.toSeq,
      SIMPLE_KPIS.map {
        case (key, kpi) =>
          val kpiDef = kpis(kpi)
          BatchKpi(
            key,
            kpiDef.decimalPlaces,
            kpiCalculator.calculate(kpiDef, 1),
            kpiUnitOpt = kpiDef.kpiUnit,
          )
      }.toSeq,
    )

  }

  def listEntryAnimalsInternal(batchIds: Seq[BatchId])(implicit dbSession: DBSession): Seq[BatchEntryAnimals] = {
    sql"""|select
          |  pqin.location_id as batchId,
          |  tin.actor_date as date,
          |  pqin.pigamount as amount,
          |  pqin.age as age
          |from transferin as tin
          |  left outer join pigqualityin pqin
          |    ON tin.id = pqin.transferin_id
          |where pqin.location_id in ($batchIds)
          |union all
          |select
          | pqloc.tolocation_id as batchId,
          | tloc.actor_date as date,
          | pqloc.pigamount as amount,
          | pqloc.age as age
          |from transferloc as tloc
          |  left outer join pigqualityloc as pqloc
          |    on pqloc.transferloc_id = tloc.id
          |where pqloc.tolocation_id in ($batchIds)""".stripMargin
      .map(rs => {
        BatchEntryAnimals(
          rs.long("batchId"),
          rs.instant("date"),
          rs.intOpt("amount"),
          rs.intOpt("age"),
        )
      })
      .list
      .apply()
  }

  def computeAverageExitAgeInternalOpt(
    batchIds: Seq[BatchId],
    exitDate: Long,
    entryAnimals: Seq[BatchEntryAnimals],
  ): Seq[(BatchId, Option[Int])] = {
    batchIds.map { batchId =>
      val batchEntryAnimals: Seq[BatchEntryAnimals] = entryAnimals.filter(entryAnimals => entryAnimals.batchId == batchId)
      if (
        batchEntryAnimals.nonEmpty && batchEntryAnimals.forall(entryAnimals =>
          entryAnimals.ageOpt.isDefined && entryAnimals.amountOpt.isDefined,
        )
      ) {
        val firstEntryDate = batchEntryAnimals.map(_.date).min
        val weightedAgeOnFirstEntryDate = batchEntryAnimals.map { entryAnimals =>
          val daysBetweenEntryAndFirstEntry = Duration.between(firstEntryDate, entryAnimals.date).toDays.toInt
          (entryAnimals.ageOpt.get - daysBetweenEntryAndFirstEntry) * entryAnimals.amountOpt.get
        }
        val totalEntryAnimals = batchEntryAnimals.foldLeft(0)((acc, next) => acc + next.amountOpt.get)
        val avgAgeOnFirstEntryDate = BigDecimal(weightedAgeOnFirstEntryDate.sum) / totalEntryAnimals
        val avgAgeOnExit = avgAgeOnFirstEntryDate + Duration.between(firstEntryDate, Instant.ofEpochMilli(exitDate)).toDays

        val roundedAvgAge = avgAgeOnExit.setScale(0, RoundingMode.HALF_UP).toInt
        batchId -> Some(roundedAvgAge)
      } else batchId -> None
    }
  }

  def withTenant(t: AutowireContext) = new TenantWrapper(t)

  case class LastEvent(gridLabelKey: String, instant: Instant)

}
