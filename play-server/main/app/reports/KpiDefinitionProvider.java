package reports;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Preconditions;
import domains.kpi.KpiDefinitionService;
import org.jooq.DSLContext;
import org.jooq.Record1;
import org.jooq.Select;
import play.i18n.Messages;
import play.libs.Json;
import scala.jdk.CollectionConverters;
import tenant.AccountFarmLangSession;
import utils.Utils;

import java.util.*;
import java.util.stream.Collectors;

import static jooq.farm.tables.Settings.SETTINGS;
import static jooq.public_.Tables.COMMUNITY_KPI_DEFINITION;
import static jooq.public_.Tables.COMMUNITY_ORGANIZATION_BENCHMARK;
import static org.jooq.impl.DSL.select;

/**
 * Created by <PERSON> on 26.1.2015.
 */
public class KpiDefinitionProvider {
    public interface MessageProvider {
        boolean isDefined(String key);
        String get(String key);
    }
    public static class DummyMessageProvider implements MessageProvider {
        @Override
        public boolean isDefined(String key) {
            return true;
        }

        @Override
        public String get(String key) {
            return key;
        }
    }
    public static final String REPORT_BREED_EFFICIENCY = "report.breed_efficiency";
    public static final String REPORT_ACCOUNTING = "report.accounting";
    public static final String REPORT_FATTENER_EFFICIENCY = "report.fattener_efficiency";
    public static final String REPORT_LOCATION_EFFICIENCY = "report.location_efficiency";
    public static final String REPORT_WEANER_EFFICIENCY = "report.weaner_efficiency";
    public static final String REPORT_GILT_EFFICIENCY = "report.gilt_efficiency";
    public static final String REPORT_PRODUCTION_OVERVIEW = "report.production_overview";
    public static final String REPORT_PRODUCTION = "report.production";
    public static final String REPORT_PARITY = "report.parity";
    public static final String REPORT_LACTATION_DAYS = "report.lactationdays";
    public static final String HIDDEN_KPIS_POSTFIX = "_h"; //
    public static final String REPORT_FEED_EFFICIENCY = "report.feed";
    public static final String COMMUNITY_BENCHMARK_REPORT_SETTINGS_PREFIX = "report.custom.community_benchmark.";

    public static LinkedHashMap<String, List<reactapp.shared.kpi.Kpi>> provide(Messages messages, String reportId, KpiDefinitionService kpiDefinitionService, AccountFarmLangSession tt) {
        return provide(messages, reportId,null, kpiDefinitionService, tt);
    }

    public static LinkedHashMap<String, List<reactapp.shared.kpi.Kpi>> provide(Messages messages, String reportId, Set<String> chosenKpis, KpiDefinitionService kpiDefinitionService, AccountFarmLangSession tt) {
        final DSLContext db = tt.dslContext();
        LinkedHashMap<String, List<reactapp.shared.kpi.Kpi>> result = new LinkedHashMap<>();
        ArrayNode reportDef = null;
        if (reportId.startsWith(COMMUNITY_BENCHMARK_REPORT_SETTINGS_PREFIX)) {
            reportDef = getBenchmarkCommunityReportDefinition(
                db,
                Long.parseLong(reportId.substring(COMMUNITY_BENCHMARK_REPORT_SETTINGS_PREFIX.length())),
                Optional.empty()
            );
        } else {
            reportDef = getReportDefinition(db, reportId);
        }
        Set<String> kpisToHide= null;

        if(chosenKpis==null || chosenKpis.size()==0) {//nothing from view so use hidden kpi from settings
            kpisToHide = getHiddenList(db, reportId);
        }
        Map<String, reactapp.shared.kpi.Kpi> kpiDefinitions = scala.jdk.javaapi.CollectionConverters.asJava(
            kpiDefinitionService.getStaticKpiDefinitionsMap(tt.dbSession(), tt.user(), tt.lang())
        );
        Map<String, List<reactapp.shared.kpi.Kpi>> customKpis = getReportCustomization(messages, kpiDefinitions, kpisToHide,chosenKpis, db, reportId);

        for (JsonNode jn : reportDef) {
            String sectionMessageKey = Utils.getValue(jn, "messageKey", String.class);

            JsonNode kpiNodes = jn.get("kpis");
            List<reactapp.shared.kpi.Kpi> resolvedKpis = new ArrayList<>();

            if (kpiNodes != null && kpiNodes.isArray()) {
                for (JsonNode kn : kpiNodes) {

                    String kpiCode = Utils.getValue(kn, "code", String.class);
                    if ((kpisToHide!=null && !kpisToHide.contains(kpiCode)) || (chosenKpis!=null && chosenKpis.contains(kpiCode))) {

                        Integer days = Utils.getValue(kn, "days", Integer.class, null);
                        String messageKey = Utils.getValue(kn, "messageKey", String.class, null);
                        reactapp.shared.kpi.Kpi kpiDef = kpiDefinitions.get(kpiCode);

                        if (kpiDef != null) {
                            String name = (messageKey != null && messages.isDefinedAt(messageKey)) ? messages.at(messageKey) : kpiDef.name();
                            resolvedKpis.add(kpiDef.withDays(days).withName(name));
                        }
                    }
                    if (customKpis.containsKey(kpiCode)) {
                        resolvedKpis.addAll(customKpis.get(kpiCode));
                    }
                }
            }
            if (resolvedKpis.size() > 0) {
                result.put(messages.at(sectionMessageKey), resolvedKpis);
            }
        }
        return result;
    }

    private static Map<String, List<reactapp.shared.kpi.Kpi>> getReportCustomization(
        Messages messages,
        Map<String, reactapp.shared.kpi.Kpi> kpiDefinitions,
        Set<String> kpisToHide,
        Set<String> chosenKpis,
        DSLContext db,
        String reportId
    ) {
        HashMap<String, List<reactapp.shared.kpi.Kpi>> result = new HashMap<>();
        ArrayNode reportDefCustomization = getReportDefinitionCustomization(db, reportId);

        for (JsonNode jn : reportDefCustomization) {
            String positionKpi = Utils.getValue(jn, "positionKpi", String.class);
            JsonNode customKpis = jn.get("kpis");

            if (customKpis != null && customKpis.isArray()) {
                List<reactapp.shared.kpi.Kpi> resolvedKpis = new ArrayList<>();

                for (JsonNode customKpi : customKpis) {
                    String kpiCode = Utils.getValue(customKpi, "code", String.class);
                    if ( (kpisToHide!=null && !kpisToHide.contains(kpiCode)) || (chosenKpis!=null && chosenKpis.contains(kpiCode))) {
                        Integer days = Utils.getValue(customKpi, "days", Integer.class, null);
                        String messageKey = Utils.getValue(customKpi, "messageKey", String.class, null);
                        reactapp.shared.kpi.Kpi kpiDef = kpiDefinitions.get(kpiCode);

                        if (kpiDef != null) {
                            String name = (messageKey != null && messages.isDefinedAt(messageKey)) ? messages.at(messageKey) : kpiDef.name();
                            resolvedKpis.add(kpiDef.withDays(days).withName(name));
                        }
                    }
                }
                if (!resolvedKpis.isEmpty()) {
                    result.put(positionKpi, resolvedKpis);
                }
            }
        }
        return result;
    }

    public static ArrayNode getBenchmarkCommunityReportDefinition(DSLContext db, Long communityId, Optional<String> communityName) {
        ObjectMapper om = Utils.mapper();
        List<ObjectNode> kpis = db
            .select(COMMUNITY_KPI_DEFINITION.KPI_DEF_CODE)
            .from(COMMUNITY_ORGANIZATION_BENCHMARK)
            .join(COMMUNITY_KPI_DEFINITION)
            .on(COMMUNITY_KPI_DEFINITION.COMMUNITY_ID.eq(COMMUNITY_ORGANIZATION_BENCHMARK.ID))
            .where(COMMUNITY_KPI_DEFINITION.COMMUNITY_ID.eq(communityId))
            .and(COMMUNITY_ORGANIZATION_BENCHMARK.IS_ACTIVE)
            .orderBy(COMMUNITY_KPI_DEFINITION.ID)
            .fetch(COMMUNITY_KPI_DEFINITION.KPI_DEF_CODE).stream().map(kpi -> om.createObjectNode().put("code", kpi)).collect(Collectors.toList());

        ObjectNode json = om.createObjectNode();
        json.put("sectionCode", "kpis");
        json.put("messageKey", "js.label.labelcolumn");
        communityName.ifPresent(name -> json.put("localizedName", name));
        json.set("kpis", om.createArrayNode().addAll(kpis));
        return om.createArrayNode().add(json);
    }

    private static ArrayNode getReportDefinition(DSLContext db, String settingId) {
        String v = db.select(SETTINGS.ENTRYVALUE).distinctOn(SETTINGS.NAME)
                .from(SETTINGS).where(SETTINGS.NAME.eq(settingId))
                .orderBy(SETTINGS.NAME.asc(), SETTINGS.PRIORITY.desc())
                .fetchOne(SETTINGS.ENTRYVALUE);

        Preconditions.checkState(v != null, "Required report can not be found: %s", settingId);
        JsonNode jn = Json.parse(v);
        Preconditions.checkState(jn.isArray(), "Required report '%s' is in not valid: %s", settingId, v);
        return (ArrayNode) jn;
    }

    private static ArrayNode getReportDefinitionCustomization(DSLContext db, String settingId) {
        Select<Record1<String>> customSelect = select(SETTINGS.ENTRYVALUE).distinctOn(SETTINGS.NAME)
                .from(SETTINGS).where(SETTINGS.NAME.eq(settingId.concat(".c")))
                .orderBy(SETTINGS.NAME.asc(), SETTINGS.PRIORITY.desc());
        if (db.fetchExists(customSelect)) {
            customSelect.attach(db.configuration());
            String v = customSelect.fetchOne(SETTINGS.ENTRYVALUE);
            JsonNode jn = Json.parse(v);
            Preconditions.checkState(jn.isArray(), "Efficiency report for breeding animals in not valid: %s", v);
            return (ArrayNode) jn;
        } else {
            return Utils.mapper().createArrayNode();
        }
    }

    private static Set<String> getHiddenList(DSLContext db, String settingId) {
        Set<String> res = new HashSet<>();
        String v = db.select(SETTINGS.ENTRYVALUE).distinctOn(SETTINGS.NAME)
                .from(SETTINGS).where(SETTINGS.NAME.eq(settingId + HIDDEN_KPIS_POSTFIX))
                .orderBy(SETTINGS.NAME.asc(), SETTINGS.PRIORITY.desc())
                .fetchOne(SETTINGS.ENTRYVALUE);

        if (v != null) {
            JsonNode jn = Json.parse(v);

            if (jn.isArray()) {
                for (JsonNode code : jn) {
                    if (code.isTextual())
                        res.add(code.textValue());
                }
            }
        }
        return res;
    }

}
