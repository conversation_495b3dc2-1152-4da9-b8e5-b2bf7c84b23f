package reports.kpi.calculators.data.analysis;

import com.cloudfarms.settings.Settings;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import reactapp.shared.kpi.KpiDescription;
import reports.kpi.calculators.data.KpiDataSource;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.*;

import java.sql.Connection;
import java.sql.Date;
import java.sql.Timestamp;

import static jooq.farm.Tables.*;
import static jooq.farm.tables.Dead.DEAD;
import static jooq.farm.tables.Hop.HOP;
import static jooq.farm.tables.Weaned.WEANED;
import static org.jooq.impl.DSL.*;
import static org.jooq.util.postgres.PostgresDataType.TIMESTAMPWITHTIMEZONE;
import static reports.kpi.calculators.data.analysis.CategoryQueryProvider.*;
import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by <PERSON> on 18.11.2014.
 */
@Deprecated(since = "AI Redo to scala")
public class NonProductiveDaysKpisDataSourceSowAnalysis extends AbstractSowAnalysisKpiDataSource<Pair<Integer, Integer>> implements KpiDataSourceRegistrar {
    private final static String NPD_WEANING2SERVING = "NPD_WEANING2SERVING";
    private static final Field<Long> F_NPD_WEANING2SERVING = kpiField(NPD_WEANING2SERVING, Long.class);
    private final static String NPD_WEANING2DEAD = "NPD_WEANING2DEAD";
    private static final Field<Long> F_NPD_WEANING2DEAD = kpiField(NPD_WEANING2DEAD, Long.class);
    private final static String NPD_WEANING2SOLD = "NPD_WEANING2SOLD";
    private static final Field<Long> F_NPD_WEANING2SOLD = kpiField(NPD_WEANING2SOLD, Long.class);
    private final static String NPD_SERVING2SERVING = "NPD_SERVING2SERVING";
    private static final Field<Long> F_NPD_SERVING2SERVING = kpiField(NPD_SERVING2SERVING, Long.class);
    private final static String NPD_SERVING2SOLD = "NPD_SERVING2SOLD";
    private static final Field<Long> F_NPD_SERVING2SOLD = kpiField(NPD_SERVING2SOLD, Long.class);
    private final static String NPD_SERVING2DEAD = "NPD_SERVING2DEAD";
    private static final Field<Long> F_NPD_SERVING2DEAD = kpiField(NPD_SERVING2DEAD, Long.class);
    private final static String NPD_LATE_ABORTION = "NPD_LATE_ABORTION";
    private static final Field<Long> F_NPD_LATE_ABORTION = kpiField(NPD_LATE_ABORTION, Long.class);

    private static final Field<Timestamp> F_HOP_FROM_DATETIME = field("hop_from_datetime", Timestamp.class);
    private static final Field<Timestamp> F_HOP_TO_DATETIME = field("hop_to_datetime", Timestamp.class);
    private static final Field<Date> F_HOP_FROM_DATE = field("hop_from_date", Date.class);
    private static final Field<Date> F_HOP_TO_DATE = field("hop_to_date", Date.class);

    private static final Field<Boolean> IS_WEANED2SERVING = field("is_weaned2serving", Boolean.class);
    private static final Field<Boolean> IS_WEANED2DEAD = field("is_weaned2dead", Boolean.class);
    private static final Field<Boolean> IS_SERVING2SERVING = field("is_serving2serving", Boolean.class);
    private static final Field<Boolean> IS_SERVING2DEAD = field("is_serving2dead", Boolean.class);
    private static final Field<Boolean> IS_LATE_ABORTION = field("is_late_abortion", Boolean.class);
    private static final Field<Long> DEAD_ID = field("dead_id", Long.class);

    private static final Field<Long> F_HOP_ID = field("hop_id", Long.class);
    private static final Field<Long> F_HOP_BUYING_ID = field("hop_buying_id", Long.class);
    private static final Field<Boolean> F_HOP_HAS_SERVING = field("hop_has_serving", Boolean.class);
    private static final Field<Long> F_HOP_FARROWING_ID = field("hop_farrowing_id", Long.class);
    private static final Field<Long> F_HOP_WEANED_ID = field("hop_weaned_id", Long.class);
    private static final Field<Long> F_HOP_DEAD_ID = field("hop_dead_id", Long.class);
    private static final Field<Timestamp> F_HOP_ACTOR_DATE = field("hop_actor_date", Timestamp.class);
    private static final Field<Integer> ROW_NUMB = field("row_numb", Integer.class);
    private final SettingsHandler settingsHandler;

    public NonProductiveDaysKpisDataSourceSowAnalysis(AbstractSowAnalysisKpisDataSourceBuilder builder, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        super(builder, user, conn);
        this.settingsHandler = settingsHandler;
    }

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        registry.addKpiDataSource(NPD_WEANING2SERVING, getNpdWeaning2Serving(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_WEANING2DEAD, getNpdWeaning2Dead(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_WEANING2SOLD, getNpdWeaning2Sold(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_SERVING2SERVING, getNpdServing2Serving(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_SERVING2DEAD, getNpdServing2Dead(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_SERVING2SOLD, getNpdServing2Sold(), KpiDescription.SowAnalysisKpi());
        registry.addKpiDataSource(NPD_LATE_ABORTION, getNpdLateAbortion(), KpiDescription.SowAnalysisKpi());
    }

    public KpiDataSource getNpdWeaning2Serving() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_WEANING2SERVING);
    }

    public KpiDataSource getNpdWeaning2Dead() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_WEANING2DEAD);
    }

    public KpiDataSource getNpdWeaning2Sold() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_WEANING2SOLD);
    }

    public KpiDataSource getNpdServing2Serving() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_SERVING2SERVING);
    }

    public KpiDataSource getNpdServing2Dead() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_SERVING2DEAD);
    }

    public KpiDataSource getNpdServing2Sold() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_SERVING2SOLD);
    }

    public KpiDataSource getNpdLateAbortion() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_NPD_LATE_ABORTION);
    }

    @Override
    protected Select getOrgQuery(Long orgId, String orgTimeZone, String orgSchema, CategoryQueryProvider categoryQueryProvider) {
        Settings settings = settingsHandler.farm(orgId).settings();
        int minPregnancyDays = settings.pregnancyDaysMin();
        boolean treatZeroLiveBornAsAbortion = settings.abortionNoLiveborn();
        boolean isWeaningProductiveDay = settings.isWeaningProductiveDay();
        CommonTableExpression<Record1<Integer>> categories = categoryQueryProvider.buildCategoriesQuery();
        CommonTableExpression<Record6<Long, Integer, Timestamp, Timestamp, Date, Date>> sowCategories = categoryQueryProvider.buildSowCategoriesQuery(orgId, orgTimeZone, orgSchema);

        WindowDefinition servingEventWindow = name("servingEventWindow").as(
            partitionBy(coalesce(SERVINGEVENT.SERVING_ID, HOP.ID))
                .orderBy(HOP.SOW_ID,SERVINGEVENT.ACTOR_DATE, SERVINGEVENT.ID)
        );

        CommonTableExpression sowsHopWithFirstServingEvents = name("sows_hop_with_first_serving_events").as(
            select().
                from(
                    select(
                        HOP.ID.as(F_HOP_ID),
                        HOP.SOW_ID.as(F_CATEGORY_SOW_ID),
                        HOP.ACTOR_DATE.as(F_HOP_ACTOR_DATE),
                        decode().when(SERVINGEVENT.SERVING_ID.isNotNull(), true).otherwise(false).as(F_HOP_HAS_SERVING),
                        HOP.FARROWING_ID.as(F_HOP_FARROWING_ID),
                        HOP.WEANED_ID.as(F_HOP_WEANED_ID),
                        HOP.DEAD_ID.as(F_HOP_DEAD_ID),
                        rowNumber().over(servingEventWindow).as(ROW_NUMB)
                    )
                        .from(orgTable(orgId, HOP))
                        .leftOuterJoin(orgTable(orgId, SERVINGEVENT))
                        .on(HOP.SERVINGEVENT_ID.eq(SERVINGEVENT.ID))
                        .where(HOP.SOW_ID.isNotNull())
                        .window(servingEventWindow)
                    .asTable("windowed_hop")
                ).where(ROW_NUMB.eq(1))
        );

        CommonTableExpression cteHopWithBoughtSows = name("hop_with_bought_sows").as(
            select(
                inline(-1L).as(F_HOP_ID),
                TRANSFERINDIVIDUALIN.SOW_ID.as(F_CATEGORY_SOW_ID),
                TRANSFERIN.ACTOR_DATE.as(F_HOP_ACTOR_DATE),
                TRANSFERIN.ID.as(F_HOP_BUYING_ID),
                decode().when(WEANED.ID.isNull(), value(true)).otherwise(false).as(F_HOP_HAS_SERVING),
                castNull(Long.class).as(F_HOP_FARROWING_ID),
                decode().when(WEANED.ID.isNull(), castNull(Long.class)).otherwise(WEANED.ID).as(F_HOP_WEANED_ID),
                castNull(Long.class).as(F_HOP_DEAD_ID),
                sowCategories.field(F_CATEGORY_ID),
                sowCategories.field(F_CATEGORY_FROM_DATE),
                sowCategories.field(F_CATEGORY_TO_DATE)
            ).distinctOn(TRANSFERINDIVIDUALIN.SOW_ID)
                .from(orgTable(orgSchema, TRANSFERINDIVIDUALIN))
                .join(orgTable(orgSchema, TRANSFERIN))
                .on(TRANSFERIN.ID.eq(TRANSFERINDIVIDUALIN.TRANSFERIN_ID))
                .join(sowCategories)
                .on(sowCategories.field(F_CATEGORY_SOW_ID).eq(TRANSFERINDIVIDUALIN.SOW_ID))
                .join(orgTable(orgSchema, SERVING))
                .on(SERVING.SOW_ID.eq(TRANSFERINDIVIDUALIN.SOW_ID).and(SERVING.LOCATION_ID.isNull()))
                .leftOuterJoin(orgTable(orgSchema, WEANED))
                .on(WEANED.SERVING_ID.eq(SERVING.ID).and(WEANED.NURSERY.isFalse()).and(WEANED.SOW_LOCATION_ID.isNull()))
                .orderBy(TRANSFERINDIVIDUALIN.SOW_ID.asc(), SERVING.ACTOR_DATE.desc(), SERVING.ID.desc(), WEANED.ACTOR_DATE.desc(), WEANED.ID.desc())
                .union(
                    select(
                        sowsHopWithFirstServingEvents.field(F_HOP_ID),
                        sowsHopWithFirstServingEvents.field(F_CATEGORY_SOW_ID),
                        sowsHopWithFirstServingEvents.field(F_HOP_ACTOR_DATE),
                        castNull(Long.class).as(F_HOP_BUYING_ID),
                     //   sowsHopWithFirstServingEvents.field(F_HOP_SERVINGEVENT_ID),
                        sowsHopWithFirstServingEvents.field(F_HOP_HAS_SERVING),
                        sowsHopWithFirstServingEvents.field(F_HOP_FARROWING_ID),
                        sowsHopWithFirstServingEvents.field(F_HOP_WEANED_ID),
                        sowsHopWithFirstServingEvents.field(F_HOP_DEAD_ID),
                        sowCategories.field(F_CATEGORY_ID),
                        sowCategories.field(F_CATEGORY_FROM_DATE),
                        sowCategories.field(F_CATEGORY_TO_DATE)
                    )
                        .from(sowCategories)
                        .join(sowsHopWithFirstServingEvents)
                        .on(sowCategories.field(F_CATEGORY_SOW_ID).eq(sowsHopWithFirstServingEvents.field(F_CATEGORY_SOW_ID)))
                        .leftOuterJoin(orgTable(orgId, DEAD))
                        .on(sowCategories.field(F_CATEGORY_SOW_ID).eq(DEAD.SOW_ID))
                        .where(
                            DEAD.ID.isNull().or(sowsHopWithFirstServingEvents.field(F_HOP_ACTOR_DATE).le(DEAD.ACTOR_DATE))
                                .and(
                                sowsHopWithFirstServingEvents.field(F_HOP_HAS_SERVING).isTrue()
                                    .or(sowsHopWithFirstServingEvents.field(F_HOP_WEANED_ID).isNotNull())
                                    .or(sowsHopWithFirstServingEvents.field(F_HOP_FARROWING_ID).isNotNull())
                                    .or(sowsHopWithFirstServingEvents.field(F_HOP_DEAD_ID).isNotNull())
                            )
                        )
                )
        );

        Field<Integer> fieldHopOrder = decode()
            .when(F_HOP_BUYING_ID.isNotNull(), inline(0))
            .when(F_HOP_FARROWING_ID.isNotNull(), inline(1))
            .when(F_HOP_WEANED_ID.isNotNull(), inline(2))
            .when(F_HOP_HAS_SERVING.isTrue(), inline(3))
            .when(F_HOP_DEAD_ID.isNotNull(), inline(5))
            .otherwise(inline(4));

        Field<?>[] hopOrdering = new Field[]{
                F_CATEGORY_SOW_ID,
                F_CATEGORY_ID,
                F_HOP_ACTOR_DATE,
                fieldHopOrder,
                F_HOP_ID
        };

        WindowDefinition hopWindow = name("w").as(
                orderBy(hopOrdering)
        );

        CommonTableExpression sowCycles = name("sow_cycles").as(
                select(
                        F_CATEGORY_SOW_ID,
                        F_CATEGORY_ID,
                        F_CATEGORY_FROM_DATE,
                        F_CATEGORY_TO_DATE,
                        F_HOP_ACTOR_DATE.as(F_HOP_FROM_DATETIME),
                        decode()
                            .when(lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID).and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow))),
                                lead(F_HOP_ACTOR_DATE, 1, infinity(TIMESTAMPWITHTIMEZONE)).over(hopWindow)
                            )
                            .otherwise(infinity(TIMESTAMPWITHTIMEZONE)).as(F_HOP_TO_DATETIME),
                        asDateAtTimeZoneNotInCondition(F_HOP_ACTOR_DATE, orgTimeZone).as(F_HOP_FROM_DATE),
                        asDateAtTimeZoneNotInCondition(decode()
                            .when(lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID).and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow))),
                                lead(F_HOP_ACTOR_DATE, 1, infinity(TIMESTAMPWITHTIMEZONE)).over(hopWindow)
                            )
                            .otherwise(infinity(TIMESTAMPWITHTIMEZONE)), orgTimeZone).as(F_HOP_TO_DATE),
                        decode()
                            .when(
                                lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID)
                                .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                .and(F_HOP_WEANED_ID.isNotNull()).and(lead(F_HOP_HAS_SERVING).over(hopWindow))
                                .or(
                                    lead(F_CATEGORY_SOW_ID).over(hopWindow).isDistinctFrom(F_CATEGORY_SOW_ID)
                                    .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                    .and(F_HOP_WEANED_ID.isNotNull()).and(WEANED.NURSERY.isFalse())
                                    .and(lag(not(F_HOP_HAS_SERVING)).over(hopWindow))),
                                true
                            )
                            .otherwise(false).as(IS_WEANED2SERVING),
                        decode()
                            .when(lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID)
                                .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                .and(F_HOP_WEANED_ID.isNotNull())
                                .and(lead(F_HOP_DEAD_ID).over(hopWindow).isNotNull()),
                                true
                            )
                            .otherwise(false).as(IS_WEANED2DEAD),
                        decode()
                            .when(lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID)
                                .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                .and(F_HOP_HAS_SERVING).and(lead(F_HOP_DEAD_ID).over(hopWindow).isNotNull()),
                                true
                            )
                            .otherwise(false).as(IS_SERVING2DEAD),
                        decode()
                            .when(lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID)
                                .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                .and(F_HOP_HAS_SERVING)
                                .and(lead(F_HOP_HAS_SERVING).over(hopWindow)),
                                true
                            )
                            .otherwise(false).as(IS_SERVING2SERVING),
                        decode().when
                            (
                                lead(F_CATEGORY_SOW_ID).over(hopWindow).isNotDistinctFrom(F_CATEGORY_SOW_ID)
                                .and(F_CATEGORY_ID.isNotDistinctFrom(lead(F_CATEGORY_ID).over(hopWindow)))
                                .and(inline(treatZeroLiveBornAsAbortion))
                                .and(F_HOP_HAS_SERVING)
                                .and(lead(F_HOP_FARROWING_ID).over(hopWindow).isNotNull())
                                .and(lead(coalesce(SERVING.LIVEBORN, 0)).over(hopWindow).eq(inline(0)))
                                .and(lead(dateDiff(asDateAtTimeZoneNotInCondition(SERVING.FARROW_ENDTIME, orgTimeZone), asDateAtTimeZoneNotInCondition(SERVING.ACTOR_DATE, orgTimeZone))).over(hopWindow).lt(minPregnancyDays)),
                                true
                            )
                            .otherwise(false).as(IS_LATE_ABORTION),
                        lead(F_HOP_DEAD_ID).over(hopWindow).as(DEAD_ID)
                )
                        .from(cteHopWithBoughtSows)
                        .leftOuterJoin(orgTable(orgSchema, WEANED)).on(F_HOP_WEANED_ID.eq(WEANED.ID))
                        .leftOuterJoin(orgTable(orgSchema, SERVING)).on(F_CATEGORY_SOW_ID.eq(SERVING.SOW_ID).and(F_HOP_FARROWING_ID.eq(SERVING.ID)))
                        .window(hopWindow)
                        .orderBy(hopOrdering)
        );

        //FIXME: if timestamp comparison could be applied, use F_CATEGORY_TO_DATETIME instead of F_CATEGORY_TO_DATE
        Condition fromDatetimeBetweenDates = sowCycles.field(F_HOP_FROM_DATETIME).ge(asTimestampAtTimeZone(sowCycles.field(F_CATEGORY_FROM_DATE), orgTimeZone))
            .and(sowCycles.field(F_HOP_FROM_DATETIME).lt(asTimestampAtTimeZoneNextDay(sowCycles.field(F_CATEGORY_TO_DATE), orgTimeZone)));

        //FIXME: if timestamp comparison could be applied, use F_CATEGORY_TO_DATETIME instead of F_CATEGORY_TO_DATE
        Condition toDatetimeBetweenDates = sowCycles.field(F_HOP_TO_DATETIME).ge(asTimestampAtTimeZone(sowCycles.field(F_CATEGORY_FROM_DATE), orgTimeZone))
            .and(sowCycles.field(F_HOP_TO_DATETIME).lt(asTimestampAtTimeZoneNextDay(sowCycles.field(F_CATEGORY_TO_DATE), orgTimeZone)));

        //FIXME: if timestamp comparison could be applied, use F_CATEGORY_TO_DATETIME instead of F_CATEGORY_TO_DATE
        Condition wholeIntervalBetweenDates = sowCycles.field(F_HOP_FROM_DATETIME).lt(asTimestampAtTimeZone(sowCycles.field(F_CATEGORY_FROM_DATE), orgTimeZone))
            .and(sowCycles.field(F_HOP_TO_DATETIME).ge(asTimestampAtTimeZoneNextDay(sowCycles.field(F_CATEGORY_TO_DATE), orgTimeZone)));

        CommonTableExpression nonProductiveDays = name("non_productive_days").as(
                select(
                        sowCycles.field(F_CATEGORY_ID),
                        sum(decode()
                                .when(sowCycles.field(IS_WEANED2SERVING).isTrue(),
                                    greatest(
                                        dateDiff(
                                            least(sowCycles.field(F_HOP_TO_DATE), plusDays(sowCycles.field(F_CATEGORY_TO_DATE), inline(1))),
                                            greatest(sowCycles.field(F_CATEGORY_FROM_DATE), plusDays(sowCycles.field(F_HOP_FROM_DATE), inline(isWeaningProductiveDay ? 1 : 0)))
                                        ),
                                        inline(0)
                                    )
                                )
                                .otherwise(inline(0))).as(F_NPD_WEANING2SERVING),
                        sum(decode()
                                .when(sowCycles.field(IS_WEANED2DEAD).isTrue().and(DEAD.DEATHTYPE_CODE.isNotNull()),
                                    greatest(
                                        dateDiff(
                                            least(sowCycles.field(F_HOP_TO_DATE), plusDays(sowCycles.field(F_CATEGORY_TO_DATE), inline(1))),
                                            greatest(sowCycles.field(F_CATEGORY_FROM_DATE), plusDays(sowCycles.field(F_HOP_FROM_DATE), inline(isWeaningProductiveDay ? 1 : 0)))
                                        ),
                                        inline(0)
                                    )
                                )
                                .otherwise(inline(0))).as(F_NPD_WEANING2DEAD),
                        sum(decode()
                                .when(sowCycles.field(IS_WEANED2DEAD).isTrue().and(DEAD.ID.isNotNull()).and(DEAD.DEATHTYPE_CODE.isNull()),
                                    greatest(
                                        dateDiff(
                                            least(sowCycles.field(F_HOP_TO_DATE), plusDays(sowCycles.field(F_CATEGORY_TO_DATE), inline(1))),
                                            greatest(sowCycles.field(F_CATEGORY_FROM_DATE), plusDays(sowCycles.field(F_HOP_FROM_DATE), inline(isWeaningProductiveDay ? 1 : 0)))
                                        ),
                                        inline(0)
                                    )
                                )
                                .otherwise(inline(0))).as(F_NPD_WEANING2SOLD),
                        sum(decode()
                                .when(sowCycles.field(IS_SERVING2SERVING).isTrue().and(sowCycles.field(F_HOP_TO_DATE).between(sowCycles.field(F_CATEGORY_FROM_DATE), sowCycles.field(F_CATEGORY_TO_DATE))),
                                        dateDiff(sowCycles.field(F_HOP_TO_DATE), sowCycles.field(F_HOP_FROM_DATE)))
                                .otherwise(inline(0))).as(F_NPD_SERVING2SERVING),
                        sum(decode()
                                .when(sowCycles.field(IS_SERVING2DEAD).isTrue().and(DEAD.DEATHTYPE_CODE.isNotNull()).and(sowCycles.field(F_HOP_TO_DATE).between(sowCycles.field(F_CATEGORY_FROM_DATE), sowCycles.field(F_CATEGORY_TO_DATE))),
                                        dateDiff(sowCycles.field(F_HOP_TO_DATE), sowCycles.field(F_HOP_FROM_DATE)))
                                .otherwise(inline(0))).as(F_NPD_SERVING2DEAD),
                        sum(decode()
                                .when(sowCycles.field(IS_SERVING2DEAD).isTrue().and(DEAD.ID.isNotNull()).and(DEAD.DEATHTYPE_CODE.isNull()).and(TRANSFEROUT.LIVESELL.isDistinctFrom(inline(true))).and(sowCycles.field(F_HOP_TO_DATE).between(sowCycles.field(F_CATEGORY_FROM_DATE), sowCycles.field(F_CATEGORY_TO_DATE))),
                                        dateDiff(sowCycles.field(F_HOP_TO_DATE), sowCycles.field(F_HOP_FROM_DATE)))
                                .otherwise(inline(0))).as(F_NPD_SERVING2SOLD),
                        sum(decode().when(sowCycles.field(IS_LATE_ABORTION).isTrue().and(sowCycles.field(F_HOP_TO_DATE).between(sowCycles.field(F_CATEGORY_FROM_DATE), sowCycles.field(F_CATEGORY_TO_DATE))),
                                        dateDiff(sowCycles.field(F_HOP_TO_DATE), sowCycles.field(F_HOP_FROM_DATE)))
                                .otherwise(inline(0))).as(F_NPD_LATE_ABORTION)
                )
                        .from(sowCycles)
                        .leftOuterJoin(orgTable(orgSchema, DEAD))
                        .on(sowCycles.field(DEAD_ID).eq(DEAD.ID))
                        .leftOuterJoin(orgTable(orgSchema, TRANSFERINDIVIDUALOUT))
                        .on(DEAD.SOW_ID.eq(TRANSFERINDIVIDUALOUT.SOW_ID))
                        .leftOuterJoin(orgTable(orgSchema, TRANSFEROUT))
                        .on(TRANSFEROUT.ID.eq(TRANSFERINDIVIDUALOUT.TRANSFEROUT_ID))
                        .where(
                            fromDatetimeBetweenDates
                                .or(toDatetimeBetweenDates)
                                .or(wholeIntervalBetweenDates)
                        )
                        .groupBy(sowCycles.field(F_CATEGORY_ID))
        );

        return with(categories).with(sowCategories).with(sowsHopWithFirstServingEvents).with(cteHopWithBoughtSows).with(sowCycles).with(nonProductiveDays)
                .select(
                        inline(orgId).as(F_ORG_ID),
                        categories.field(F_CATEGORY_ID),
                        F_NPD_WEANING2SERVING,
                        F_NPD_WEANING2DEAD,
                        F_NPD_WEANING2SOLD,
                        F_NPD_SERVING2SERVING,
                        F_NPD_SERVING2DEAD,
                        F_NPD_SERVING2SOLD,
                        F_NPD_LATE_ABORTION
                )
                        .from(categories)
                        .leftOuterJoin(nonProductiveDays)
                        .on(categories.field(F_CATEGORY_ID).eq(nonProductiveDays.field(F_CATEGORY_ID)))
                        .union(
                                select(
                                        inline(orgId).as(F_ORG_ID),
                                        inline(-1).as(F_CATEGORY_ID),
                                        sum(F_NPD_WEANING2SERVING).cast(Long.class),
                                        sum(F_NPD_WEANING2DEAD).cast(Long.class),
                                        sum(F_NPD_WEANING2SOLD).cast(Long.class),
                                        sum(F_NPD_SERVING2SERVING).cast(Long.class),
                                        sum(F_NPD_SERVING2DEAD).cast(Long.class),
                                        sum(F_NPD_SERVING2SOLD).cast(Long.class),
                                        sum(F_NPD_LATE_ABORTION).cast(Long.class)
                                )
                                        .from(nonProductiveDays)
                        );
    }

    @Override
    protected Field[] getAggregatedFields() {
        return new Field[] {
                F_NPD_WEANING2SERVING,
                F_NPD_WEANING2DEAD,
                F_NPD_WEANING2SOLD,
                F_NPD_SERVING2SERVING,
                F_NPD_SERVING2DEAD,
                F_NPD_SERVING2SOLD,
                F_NPD_LATE_ABORTION
        };
    }
}
