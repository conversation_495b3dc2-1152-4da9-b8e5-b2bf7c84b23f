package reports.kpi.calculators.data.individual.gilt;

import com.google.common.collect.ImmutableList;
import models.BreedingAnimal;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import play.libs.F;
import reactapp.shared.kpi.KpiDescription;
import reports.kpi.KpiUtilsDI;
import reports.kpi.calculators.data.AbstractKpiDataSource;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.ApplicationUser;

import java.sql.Connection;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static jooq.farm.Tables.*;
import static org.jooq.impl.DSL.*;
import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by <PERSON> on 29.07.2016.
 */
@Deprecated(since = "AI Redo to scala")
public class BABoughtKpisDataSource extends AbstractKpiDataSource implements KpiDataSourceRegistrar {
    private static final String BA_MALE_AMOUNT = "BA_MALE_BOUGHT_AMOUNT";
    private static final String BA_MALE_AGE = "BA_MALE_BOUGHT_AGE";
    private static final String BA_FEMALE_AMOUNT = "BA_FEMALE_BOUGHT_AMOUNT";
    private static final String BA_FEMALE_AGE = "BA_FEMALE_BOUGHT_AGE";

    private List<F.Tuple3<String, Field<Long>, Condition>> F_MALE_AMOUNT;
    private List<F.Tuple3<String, Field<Long>, Condition>> F_MALE_AGE;
    private List<F.Tuple3<String, Field<Long>, Condition>> F_FEMALE_AMOUNT;
    private List<F.Tuple3<String, Field<Long>, Condition>> F_FEMALE_AGE;
    private final SettingsHandler settingsHandler;

    public BABoughtKpisDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        super(locations, periods, user, conn);
        this.settingsHandler = settingsHandler;

        initializeBreedKpiFields(user.getFarmId());
    }

    public BABoughtKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        super(organizationsWithTimeZone, locations, periods, user, conn);
        this.settingsHandler = settingsHandler;

        initializeBreedKpiFields(organizationsWithTimeZone.keySet().toArray(new Long[organizationsWithTimeZone.size()]));
    }

    private void initializeBreedKpiFields(Long ... organizations) {
        Set<String> breeds  = KpiUtilsDI.breedingAnimalBreeds(conn, organizations);
        Condition isMale = GILT.SEX.eq(inline(BreedingAnimal.MALE_SEX));
        Condition isFemale = GILT.SEX.eq(inline(BreedingAnimal.FEMALE_SEX));

        F_MALE_AMOUNT = getKpiFieldsByBreed(BA_MALE_AMOUNT, isMale, breeds);
        F_MALE_AGE = getKpiFieldsByBreed(BA_MALE_AGE, isMale, breeds);
        F_FEMALE_AMOUNT = getKpiFieldsByBreed(BA_FEMALE_AMOUNT, isFemale, breeds);
        F_FEMALE_AGE = getKpiFieldsByBreed(BA_FEMALE_AGE, isFemale, breeds);
    }

    private List<F.Tuple3<String, Field<Long>, Condition>> getKpiFieldsByBreed(String kpiName, Condition valueCondition, Set<String> breeds) {
        return ImmutableList.<F.Tuple3<String, Field<Long>, Condition>>builder()
            .add(F.Tuple3(kpiName, kpiField(kpiName, Long.class), valueCondition))
            .addAll(breeds.stream().map(breed -> F.Tuple3(kpiName + "$" + breed, kpiField(kpiName + "$" + breed, Long.class), valueCondition.and(upper(coalesce(BREEDEQ.BREED1, GILT.BREED)).eq(breed.toUpperCase())))).iterator())
            .build();
    }

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        ImmutableList.<F.Tuple3<String, Field<Long>, Condition>>builder()
            .addAll(F_MALE_AMOUNT)
            .addAll(F_MALE_AGE)
            .addAll(F_FEMALE_AMOUNT)
            .addAll(F_FEMALE_AGE)
            .build().stream().forEach(f ->
                registry.addKpiDataSource(f._1, (orgId, categoryId) -> super.getValue(orgId, categoryId, f._2), KpiDescription.UnusedOldDataSource())
            );
    }

    @Override
    protected Select getOrgQuery(Long orgId, String orgTimeZone, String orgSchema, CommonTableExpression<Record3<Long, Date, Date>> dates) {
        String slaughterFarmNumber = settingsHandler.getFarmnumberSlaughter(user);

        Field<Integer> one = inline(1);
        Field<Integer> age = dateDiff(asDateAtTimeZoneNotInCondition(TRANSFERIN.ACTOR_DATE, orgTimeZone), GILT.BIRTHDATE);

        return with(dates)
            .select(
                inline(orgId).as(ORG_ID),
                dates.field(SEQ)
            )
            .select(getField(F_MALE_AMOUNT, one))
            .select(getField(F_MALE_AGE, age))
            .select(getField(F_FEMALE_AMOUNT, one))
            .select(getField(F_FEMALE_AGE, age))
            .from(orgTable(orgSchema, GILT))
            .join(orgTable(orgSchema, TRANSFERINDIVIDUALIN)).on(GILT.ID.eq(TRANSFERINDIVIDUALIN.GILT_ID))
            .join(orgTable(orgSchema, TRANSFERIN)).on(TRANSFERIN.ID.eq(TRANSFERINDIVIDUALIN.TRANSFERIN_ID))
            .join(dates).on(TRANSFERIN.ACTOR_DATE.ge(asTimestampAtTimeZone(dates.field(FROM_DATE), orgTimeZone)).and(TRANSFERIN.ACTOR_DATE.lt(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone))))
            .leftOuterJoin(orgTable(orgSchema, BREEDEQ)).on(upper(GILT.BREED).eq(upper(BREEDEQ.BREED2)))
            .where(
                GILT.BIRTHDATE.isNotNull()
                .and(not(GILT.ANIMALID.startsWith(slaughterFarmNumber)))
                .and(useLocations() ? TRANSFERINDIVIDUALIN.LOCATION_ID.in(getLocations()) : trueCondition())
            )
            .groupBy(dates.field(SEQ));
    }

    private List<Field<Long>> getField(List<F.Tuple3<String, Field<Long>, Condition>> fieldDefs, Field<Integer> valueExpression) {
        return fieldDefs.stream().map(fc -> sum(decode().when(fc._3, valueExpression).otherwise(inline(0))).cast(Long.class).as(fc._2)).collect(Collectors.toList());
    }

    @Override
    protected Field[] getAggregatedFields() {
        return ImmutableList.<Field<Long>>builder()
            .addAll(F_MALE_AMOUNT.stream().map(v -> v._2).iterator())
            .addAll(F_MALE_AGE.stream().map(v -> v._2).iterator())
            .addAll(F_FEMALE_AMOUNT.stream().map(v -> v._2).iterator())
            .addAll(F_FEMALE_AGE.stream().map(v -> v._2).iterator())
            .build().toArray(new Field[0]);
    }
}
