package reports.kpi.calculators.data.individual.sow;

import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import reports.kpi.KpiUtilsDI;
import reports.kpi.calculators.data.AbstractKpiDataSource;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.ApplicationUser;

import java.sql.Connection;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static jooq.farm.Tables.*;
import static jooq.farm.tables.BreedRuleView.BREED_RULE_VIEW;
import static jooq.farm.tables.Hop.HOP;
import static jooq.farm.tables.Serving.SERVING;
import static jooq.farm.tables.Sow.SOW;
import static org.jooq.impl.DSL.*;
import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by <PERSON> on 23.10.2014.
 */
public class ParityNumberKpiDataSource extends AbstractKpiDataSource implements KpiDataSourceRegistrar {
    private static final String SOW_PARITY_NUMBERS = "SOW_PARITY_NUMBERS";
    private static final String SOW_PARITY_SOW_AMOUNT = "SOW_PARITY_SOW_AMOUNT";
    private static final Field<Long> F_SOW_PARITY_NUMBERS = kpiField(SOW_PARITY_NUMBERS, Long.class);
    private static final Field<Long> F_SOW_PARITY_SOW_AMOUNT = kpiField(SOW_PARITY_SOW_AMOUNT, Long.class);

    private static final Field<Timestamp> F_DEAD_DATETIME = field("_dead_datetime", Timestamp.class);
    private static final Field<Date> F_COMPLETED_DATE = field("_completed_date", Date.class);
    private static final Field<Date> F_START_DATE = field("_start_date", Date.class);
    private static final Field<Date> F_END_DATE = field("_end_date", Date.class);
    private static final Field<Date> F_MIN_TO_DATE = field("_min_to_date", Date.class);
    private static final Field<Date> F_MAX_TO_DATE = field("_max_to_date", Date.class);
    private static final Field<Date> F_WEANING_DATE = field("_WEANING_date", Date.class);
    private static final Field<Long> F_LOCATION_ID = field("_location_id", Long.class);
    private static final Field<Long> F_SOW_ID = field("_sow_id", Long.class);
    private static final Field<Timestamp> F_SOW_DATETIME = field("_sow_datetime", Timestamp.class);
    private static final Field<Short> F_PARITY_NUMBER = field("_repr_cycle_number", Short.class);

    public ParityNumberKpiDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, Set<String> breeds, ApplicationUser user, Connection conn) {
        super(locations, periods, breeds, user, conn);
    }

    public ParityNumberKpiDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, Set<String> breeds, ApplicationUser user, Connection conn) {
        super(organizationsWithTimeZone, locations, periods, breeds, conn, user);
    }

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        registry.addKpiDataSource(SOW_PARITY_NUMBERS, (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_SOW_PARITY_NUMBERS), "Provides the sum of parities of active sows in reporting interval. Active sow means here that first insemination was before the end of reporting interval and she is still alive or was not sold to slaughterhouse in reporting interval.");
        registry.addKpiDataSource(SOW_PARITY_SOW_AMOUNT, (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_SOW_PARITY_SOW_AMOUNT), "Provides the number of active sows in reporting interval. Active sow means here that first insemination was before the end of reporting interval and she is still alive or was not sold to slaughterhouse in reporting interval.");
    }

    @Override
    protected Select getOrgQuery(Long orgId, String orgTimeZone, String orgSchema, CommonTableExpression<Record3<Long, Date, Date>> dates) {
        CommonTableExpression<Record2<Date, Date>> ctMinMaxDate = name("ct_min_max_date").as(
            select(
                    min(TO_DATE).as(F_MIN_TO_DATE),
                    max(TO_DATE).as(F_MAX_TO_DATE)
            )
            .from(dates)
        );

        CommonTableExpression<Record3<Long, Timestamp, Timestamp>> ctSows = name("ct_sows").as(
            select(
                    SOW.ID.as(F_SOW_ID),
                    SOW.SOWDATE.as(F_SOW_DATETIME),
                    DEAD.ACTOR_DATE.as(F_DEAD_DATETIME)
            )
            .from(orgTable(orgId, SOW))
            .crossJoin(ctMinMaxDate)
            .leftOuterJoin(orgTable(orgId, DEAD))
            .on(SOW.ID.eq(DEAD.SOW_ID))
            .where(
                SOW.SOWDATE.lt(asTimestampAtTimeZoneNextDay(ctMinMaxDate.field(F_MAX_TO_DATE), orgTimeZone))
                .and(DEAD.ID.isNull().or(DEAD.ACTOR_DATE.ge(asTimestampAtTimeZoneNextDay(ctMinMaxDate.field(F_MIN_TO_DATE), orgTimeZone))))
                .and(isUsingBreeds() ? SOW.BREED.in(getBreeds()) : trueCondition())
            )
        );

        CommonTableExpression ctActiveSows = name("ct_active_sows").as(
            select(
                dates.field(SEQ),
                ctSows.field(F_SOW_ID),
                dates.field(FROM_DATE),
                dates.field(TO_DATE),
                HOP.TO_LOCATION_ID.as(F_LOCATION_ID)
            ).distinctOn(dates.field(SEQ), ctSows.field(F_SOW_ID))
            .from(dates)
            .join(ctSows)
            .on(ctSows.field(F_SOW_DATETIME).lt(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone))
                .and(ctSows.field(F_DEAD_DATETIME).isNull()
                    .or(ctSows.field(F_DEAD_DATETIME).ge(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone)))
                )
            )
            .join(orgTable(orgId, HOP))
            .on(ctSows.field(F_SOW_ID).eq(HOP.SOW_ID)
                .and(HOP.ACTOR_DATE.lt(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone)))
            )
            .orderBy(dates.field(SEQ).asc(), ctSows.field(F_SOW_ID).asc(), HOP.ACTOR_DATE.desc(), HOP.ID.desc())
        );

        final SelectSeekStep4 select1 = select(
            ctActiveSows.field(SEQ).as(SEQ),
            ctActiveSows.field(F_SOW_ID).as(F_SOW_ID),
            ctActiveSows.field(F_LOCATION_ID).as(F_LOCATION_ID),
            decode()
                .when(SERVING.FARROW_ENDTIME.isNull().or(SERVING.FARROW_ENDTIME.ge(asTimestampAtTimeZoneNextDay(ctActiveSows.field(TO_DATE), orgTimeZone))),SERVING.LITTER.minus(inline(1)))
                .otherwise(SERVING.LITTER).as(F_PARITY_NUMBER)
        ).distinctOn(ctActiveSows.field(SEQ), ctActiveSows.field(F_SOW_ID))
            .from(ctActiveSows)
            .join(orgTable(orgId, SERVING))
            .on(ctActiveSows.field(F_SOW_ID).eq(SERVING.SOW_ID))
            .where(SERVING.ACTOR_DATE.lt(asTimestampAtTimeZoneNextDay(ctActiveSows.field(TO_DATE), orgTimeZone)))
            .orderBy(ctActiveSows.field(SEQ).asc(), ctActiveSows.field(F_SOW_ID).asc(), SERVING.ACTOR_DATE.desc(), SERVING.ID.desc());

        if (pedigreeBreeds != null && !pedigreeBreeds.isEmpty()) {
            select1.getQuery().addJoin(orgTable(orgSchema, BREED_RULE_VIEW), JoinType.JOIN, BREED_RULE_VIEW.SIRE_BREED.eq(SERVING.BREED).and(BREED_RULE_VIEW.DAM_BREED.eq(SOW.BREED)).and(BREED_RULE_VIEW.PROGENY_BREED.in(pedigreeBreeds)));
        }

        CommonTableExpression ctLastServing = name("ct_last_serving").as(select1);

        return with(dates)
            .with(ctMinMaxDate)
            .with(ctSows)
            .with(ctActiveSows)
            .with(ctLastServing)
            .select(
                inline(orgId).as(ORG_ID),
                ctLastServing.field(SEQ),
                sum(ctLastServing.field(F_PARITY_NUMBER)).as(F_SOW_PARITY_NUMBERS),
                countDistinct(ctLastServing.field(F_SOW_ID)).as(F_SOW_PARITY_SOW_AMOUNT)
            )
            .from(ctLastServing)
            .where(
                useLocations() ? ctLastServing.field(F_LOCATION_ID).in(getLocations()) : trueCondition()
            )
            .groupBy(SEQ);
    }

    @Override
    protected Field[] getAggregatedFields() {
        return new Field[] {F_SOW_PARITY_NUMBERS, F_SOW_PARITY_SOW_AMOUNT};
    }
}
