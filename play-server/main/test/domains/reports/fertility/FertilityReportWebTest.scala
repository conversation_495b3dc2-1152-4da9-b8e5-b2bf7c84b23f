package domains.reports.fertility

import java.time.LocalDate
import reactapp.shared.reports.fertility.{ FertilityReportColumn, FertilityReportDetailGrid, FertilityReportForm, FertilityReportGrid }
import scalatestfw._

import scala.util.Try

class FertilityReportWebTest extends WebBrowserSpec {

  override def databaseVersion: Int = 24

  "Fertility report must" must {

    "return some rows" in {

      login("reports/fertilityreportnew", farmId = Farm1.id)

      val testForm = form(FertilityReportForm.cssId, FertilityReportForm)
      val masterGrid = grid(".optional-details", FertilityReportGrid)

      testForm.enterValues(
        FertilityReportForm.From -> LocalDate.of(2013, 3, 1),
        FertilityReportForm.To -> LocalDate.of(2013, 6, 1),
      )

      cssSelector("#calculate-button").element.underlying.click()
      masterGrid.waitForSomeRows()

      masterGrid.rowByPermanentId("G10").validateRowExists()
      masterGrid.rowByPermanentId("G20").validateRowExists()
      masterGrid.rowByPermanentId("G1").validateDoesNotExist()
      masterGrid.rowByPermanentId("G28").validateDoesNotExist()
      masterGrid.rowByPermanentId("G40").validateDoesNotExist()

      masterGrid.waitForVisibleRowsCountToBe(14)
    }

    "load data into detail grid if filled cell in master grid is clicked" in {

      login("reports/fertilityreportnew", farmId = Farm1.id)

      val testForm = form(FertilityReportForm.cssId, FertilityReportForm)
      val masterGrid = grid(".optional-details", FertilityReportGrid)
      lazy val detailGrid = grid(".details", FertilityReportDetailGrid)

      testForm.enterValues(
        FertilityReportForm.From -> LocalDate.of(2013, 3, 1),
        FertilityReportForm.To -> LocalDate.of(2013, 6, 1),
      )
      cssSelector("#calculate-button").element.underlying.click()
      masterGrid.waitForSomeRows()

      masterGrid.rowByPermanentId("G20").cell(FertilityReportColumn.Dead(2)).clickMe()
      toggleDetails
      detailGrid.waitForSomeRows()

      detailGrid.waitForVisibleRowsCountToBe(1)
    }

    "produce valid exports" in {

      login("reports/fertilityreportnew", farmId = Farm1.id)

      val pageForm = form(FertilityReportForm.cssId, FertilityReportForm)
      val masterGrid = grid(".optional-details", FertilityReportGrid)

      pageForm.enterValues(
        FertilityReportForm.From -> LocalDate.of(2013, 3, 1),
        FertilityReportForm.To -> LocalDate.of(2013, 6, 1),
      )
      cssSelector("#calculate-button").element.underlying.click()
      masterGrid.waitForSomeRows()

      masterGrid.rowByPermanentId("G20").clickMe().validateRowExists()

      validateExports()
    }

    "have working form filters" in {

      login("reports/fertilityreportnew", farmId = Farm1.id)

      val testForm = form(FertilityReportForm.cssId, FertilityReportForm)
      val masterGrid = grid(".optional-details", FertilityReportGrid)

      testForm.enterValues(
        FertilityReportForm.From -> LocalDate.of(2013, 3, 1),
        FertilityReportForm.To -> LocalDate.of(2013, 6, 1),
        FertilityReportForm.Breeds -> "YY",
        FertilityReportForm.Litters -> "3..7",
      )

      def clickCalcWaitForDataAndValidate() = {
        cssSelector("#calculate-button").element.underlying.click()
        masterGrid.waitForVisibleRowsCountToBe(1)
      }

      Try(clickCalcWaitForDataAndValidate())
        .recover { case _ => clickCalcWaitForDataAndValidate() }
    }

  }

}
