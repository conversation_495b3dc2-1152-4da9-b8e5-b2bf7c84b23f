package domainz.integration.genesus

import sjsgrid.shared.form.DynamicValue

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// THIS HAS NO REAL-WORLD USAGE NOW.
// It was created as a proof of concept during the hackathon.
// It's a proof of concept that needs to be tested and refined before it can be used.
// We will start using it once we have a task regarding this domain.
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

// TODO Scaladoc explaining business mean of every field
object GenesusPersistence {
  case class InsertData(mandatoryData: InsertData.Mandatory, optionalData: OptionalData = OptionalData())

  object InsertData {
    case class Mandatory(machineTypeCode: String, managementGroup: scala.math.BigDecimal, probeType: String)
  }

  case class UpdateData(mandatoryData: UpdateData.Mandatory = UpdateData.Mandatory(), optionalData: OptionalData = OptionalData())

  object UpdateData {
    case class Mandatory(
      machineTypeCode: DynamicValue[String] = DynamicValue.Unavailable,
      managementGroup: DynamicValue[scala.math.BigDecimal] = DynamicValue.Unavailable,
      probeType: DynamicValue[String] = DynamicValue.Unavailable,
    )
  }
  case class OptionalData(
    intramuscularFatPercentage: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
    loinFatDepth: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
    loinMuscleDepth: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
  )
}
